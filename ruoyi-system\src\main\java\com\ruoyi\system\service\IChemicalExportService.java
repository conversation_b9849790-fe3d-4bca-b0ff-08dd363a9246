package com.ruoyi.system.service;

import java.util.Date;
import java.util.Map;

/**
 * 化学数据导出Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IChemicalExportService 
{
    /**
     * 导出CSV文件
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @return 导出结果
     */
    public Map<String, Object> exportToCSV(Date startDate, Date endDate, String[] layerNumbers);

    /**
     * 导出Excel文件
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @return 导出结果
     */
    public Map<String, Object> exportToExcel(Date startDate, Date endDate, String[] layerNumbers);

    /**
     * 获取导出历史记录
     * 
     * @return 导出记录列表
     */
    public Map<String, Object> getExportHistory();

    /**
     * 下载导出文件
     * 
     * @param exportId 导出记录ID
     * @return 文件信息
     */
    public Map<String, Object> downloadExportFile(Long exportId);

    /**
     * 删除导出记录
     * 
     * @param exportIds 导出记录ID数组
     * @return 删除结果
     */
    public int deleteExportRecords(Long[] exportIds);

    /**
     * 获取导出配置
     * 
     * @return 配置信息
     */
    public Map<String, Object> getExportConfig();

    /**
     * 更新导出配置
     * 
     * @param config 配置信息
     * @return 更新结果
     */
    public int updateExportConfig(Map<String, Object> config);

    /**
     * 预览导出数据
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 预览数据
     */
    public Map<String, Object> previewExportData(Date startDate, Date endDate, String[] layerNumbers, int pageNum, int pageSize);

    /**
     * 获取可选的层号列表
     * 
     * @return 层号列表
     */
    public Map<String, Object> getAvailableLayerNumbers();

    /**
     * 验证导出参数
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @return 验证结果
     */
    public Map<String, Object> validateExportParams(Date startDate, Date endDate, String[] layerNumbers);
}
