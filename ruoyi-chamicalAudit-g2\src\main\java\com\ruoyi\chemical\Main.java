package org.example;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.apache.pulsar.client.api.*;
import org.apache.pulsar.shade.com.fasterxml.jackson.databind.ObjectMapper;


import java.sql.*;
import java.text.SimpleDateFormat;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;


public class Main {
    private static final String BROKER_SERVICE_URL = "pulsar://pulsar.scc.com:6650";
    private static final String MEDICINE_TOPIC = "persistent://spc/331-interface/labMedicineData";
    private static final String TOKEN =
            "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzMxIn0.U7QrwJTShmbcucMtIqqzC5gfK8mbgsSAF20yyed1W6A";
    public static Thread taskThread;
    private static boolean isRunning = false;
    private static boolean isPaused = false;
    private static PulsarClient pulsarClient;
    private static Consumer<byte[]> consumer;
    private static final HikariDataSource dataSource;
    private static final HikariDataSource dataSourceCloud;

    static {
        // 本地数据库连接池 - 优化内存使用
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*****************************************************************");
        config.setUsername("sa");
        config.setPassword("root1234");
        config.setMaximumPoolSize(10); // 减少最大连接数以节省内存
        config.setMinimumIdle(2); // 减少最小空闲连接数
        config.setIdleTimeout(300000); // 5分钟空闲超时
        config.setMaxLifetime(1800000); // 30分钟最大生命周期
        config.setConnectionTimeout(30000); // 30秒连接超时
        config.setLeakDetectionThreshold(60000); // 1分钟泄漏检测
        dataSource = new HikariDataSource(config);
    }

    static {
        // 云数据库连接池 - 优化内存使用
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("******************************************************************************************;" +
                "sslProtocol=TLSv1");
        config.setUsername("hhh");
        config.setPassword("root1234");
        config.setMaximumPoolSize(10); // 减少最大连接数以节省内存
        config.setMinimumIdle(2); // 减少最小空闲连接数
        config.setIdleTimeout(300000); // 5分钟空闲超时
        config.setMaxLifetime(1800000); // 30分钟最大生命周期
        config.setConnectionTimeout(30000); // 30秒连接超时
        config.setLeakDetectionThreshold(60000); // 1分钟泄漏检测
        config.setPoolName("CloudDataSourcePool");
        config.setRegisterMbeans(true); // 启用JMX监控
        dataSourceCloud = new HikariDataSource(config);
    }

    /**
     * 主方法，程序的入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 定期检查新数据
        runMainLogic();
    }



    private static void processMessage(Connection connection, ObjectMapper objectMapper, Message<byte[]> msg) {
        try {
            JavaFXApp.addProcessInfo("    🔄 开始解析消息为Chemical对象");
            Chemical chemical = objectMapper.readValue(msg.getData(), Chemical.class);

            if (chemical != null && chemical.getId() != null) {
                JavaFXApp.addProcessInfo("    📝 Chemical对象解析成功，ID: " + chemical.getId());
                insertDataIntoDatabase(connection, chemical);
                JavaFXApp.addProcessInfo("    ✅ Chemical数据插入完成，ID: " + chemical.getId());
            } else {
                JavaFXApp.addProcessInfo("    ❌ Chemical对象解析失败或ID为空");
                throw new RuntimeException("Chemical对象无效");
            }
        } catch (Exception e) {
            JavaFXApp.addProcessInfo("    ❌ 处理消息时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("消息处理失败", e);
        }
    }

    // 修改 insertDataIntoDatabase 方法以插入数据到chemical表
    private static void insertDataIntoDatabase(Connection connection, Chemical entity) {
        String id = entity.getId();
        JavaFXApp.addProcessInfo("      🔄 开始插入chemical表，ID: " + id);
        // 创建 SQL 插入语句，根据表列名进行调整
        String sql = "INSERT INTO dbo.chemical (id, organization_id, attribute_id, examine_date, shift, staff, " +
                "department_code, process_set_name, process_name, product_set_name, product_name, test_set_name, " +
                "test_name, sample_size, layer_number, upper_limit, median_specification, down_limit, examine1, " +
                "examine2, created_by, last_updated_by, creation_date, last_update_date, status, frequency," +
                " " +
                "frequency_unit, slot_body_name, project_team_code, project_team_name, test_code, " +
                "adjustment_upper_limit, " +
                "adjustment_mid, adjustment_lower_limit, project_unit,insertion_time,is_exported,warning_upper_limit," +
                "warning_mid,warning_lower_limit,attribute1,attribute2,attribute3) VALUES (?, ?, ?, ?, ?, ?," +
                " ?, ?, ?, ?," +
                " ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?)";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {

            preparedStatement.setString(1, entity.getId());
            setNullableLong(preparedStatement, 2, entity.getOrganizationId());
            setNullableLong(preparedStatement, 3, entity.getAttributeId());
            preparedStatement.setTimestamp(4, entity.getExamineDate());
            preparedStatement.setString(5, entity.getShift());
            preparedStatement.setString(6, entity.getStaff());
            preparedStatement.setString(7, entity.getDepartmentCode());
            preparedStatement.setString(8, entity.getProcessSetName());
            preparedStatement.setString(9, entity.getProcessName());
            preparedStatement.setString(10, entity.getProductSetName());
            preparedStatement.setString(11, entity.getProductName());
            preparedStatement.setString(12, entity.getTestSetName());
            preparedStatement.setString(13, entity.getTestName());
            preparedStatement.setString(14, entity.getSampleSize());
            preparedStatement.setString(15, entity.getLayerNumber());
            preparedStatement.setString(16, entity.getUpperLimit());
            preparedStatement.setString(17, entity.getMedianSpecification());
            preparedStatement.setString(18, entity.getDownLimit());
            preparedStatement.setString(19, entity.getExamine1());
            preparedStatement.setString(20, entity.getExamine2());
            preparedStatement.setString(21, entity.getCreatedBy());
            preparedStatement.setString(22, entity.getLastUpdatedBy());
            preparedStatement.setTimestamp(23, entity.getCreationDate());
            preparedStatement.setTimestamp(24, entity.getLastUpdateDate());
            setNullableLong(preparedStatement, 25, entity.getStatus());
            preparedStatement.setString(26, entity.getFrequency());
            preparedStatement.setString(27, entity.getFrequencyUnit());
            preparedStatement.setString(28, entity.getSlotBodyName());
            preparedStatement.setString(29, entity.getProjectTeamCode());
            preparedStatement.setString(30, entity.getProjectTeamName());
            preparedStatement.setString(31, entity.getTestCode());
            preparedStatement.setString(32, entity.getAdjustmentUpperLimit());
            preparedStatement.setString(33, entity.getAdjustmentMid());
            preparedStatement.setString(34, entity.getAdjustmentLowerLimit());
            preparedStatement.setString(35, entity.getProjectUnit());
            preparedStatement.setTimestamp(36, new Timestamp(System.currentTimeMillis()));
            preparedStatement.setBoolean(37, false);
            preparedStatement.setString(38, entity.getWarningUpperLimit());
            preparedStatement.setString(39, entity.getWarningMid());
            preparedStatement.setString(40, entity.getWarningLowerLimit());
            preparedStatement.setString(41, entity.getAttribute1());
            preparedStatement.setString(42, entity.getAttribute2());
            preparedStatement.setString(43, entity.getAttribute3());
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                JavaFXApp.addProcessInfo("      ✅ 数据成功插入chemical表，ID: " + id + "，影响行数: " + rowsInserted);
            } else {
                JavaFXApp.addProcessInfo("      ⚠️ 插入chemical表无影响行数，ID: " + id);
            }
        } catch (SQLException e) {
            JavaFXApp.addProcessInfo("      ❌ 插入chemical表SQL异常，ID: " + id + "，错误代码: " + e.getErrorCode() + "，错误信息: " + e.getMessage());
            handleSQLException(e);
            throw new RuntimeException("chemical表插入失败: " + e.getMessage(), e);
        }
    }

    private static void setNullableLong(PreparedStatement preparedStatement, int parameterIndex, Long value) throws SQLException {
        if (value != null) {
            preparedStatement.setLong(parameterIndex, value);
        } else {
            preparedStatement.setNull(parameterIndex, Types.BIGINT);
        }
    }

    private static void handleSQLException(SQLException e) {
        if (e.getErrorCode() == 2627) {
            JavaFXApp.addProcessInfo("        ⚠️ 数据插入失败，该数据已存在（主键冲突）");
        } else if (e.getErrorCode() == 515) {
            JavaFXApp.addProcessInfo("        ❌ 数据插入失败，必填字段为空");
        } else if (e.getErrorCode() == 8152) {
            JavaFXApp.addProcessInfo("        ❌ 数据插入失败，字符串数据过长");
        } else {
            JavaFXApp.addProcessInfo("        ❌ 数据插入失败，SQL异常：错误代码 " + e.getErrorCode() + "，错误信息：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private static String getF_PRCSValueForDepartment(Connection remoteConnection, String departmentName) {
        String sql = "SELECT F_PRCS FROM dbo.PRCS_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, departmentName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_PRCS");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PRCS 值
    }

    private static String getF_TESTValueForTestName(Connection remoteConnection, String testName) {
        String sql = "SELECT F_TEST FROM dbo.TEST_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, testName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_TEST");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_TEST 值
    }

    private static String getF_PARTValueForProductName(Connection remoteConnection, String productName) {
        String sql = "SELECT F_PART FROM dbo.PART_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, productName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
//                    System.out.println(productName);
                    return resultSet.getString("F_PART");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PART 值
    }

    private static CalculatedValues calculateControlValues(ControlLimits controlLimits, String testName) {
        // 定义测试名与样本量的映射
        Map<String, Integer> sampleSizeMap = new HashMap<String, Integer>();
        sampleSizeMap.put("PD全线微蚀量", 3);
        sampleSizeMap.put("TR微蚀微蚀量", 5);
        sampleSizeMap.put("TR微蚀微蚀量(2.1线速)", 5);
        sampleSizeMap.put("TROSPOSP膜厚", 3);
        sampleSizeMap.put("TROSPOSP膜厚(2.1线速)", 3);
        double intermediateValue = 6 * controlLimits.fSp;
        // 如果测试名在映射中，根据样本量调整intermediateValue的计算
        if (sampleSizeMap.containsKey(testName)) {
            int sampleSize = sampleSizeMap.get(testName);
            intermediateValue = intermediateValue / Math.sqrt(sampleSize);
        }
        double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
        double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
        double lcl1 = (7.0 / 8.0) * lowerControlLimit + (1.0 / 8.0) * upperControlLimit;
        double ucl1 = (1.0 / 8.0) * lowerControlLimit + (7.0 / 8.0) * upperControlLimit;
        return new CalculatedValues(upperControlLimit, lowerControlLimit, lcl1, ucl1);
    }

    private static double calculateExamineValue(double examine1, CalculatedValues calculatedValues) {
        if (examine1 < calculatedValues.lcl1) {
            return calculatedValues.lcl1 + (calculatedValues.lcl1 - examine1) % (calculatedValues.ucl1 - calculatedValues.lcl1);
        } else if (examine1 > calculatedValues.ucl1) {
            return calculatedValues.ucl1 - (examine1 - calculatedValues.ucl1) % (calculatedValues.ucl1 - calculatedValues.lcl1);
        }
        return examine1;
    }

    public static void processChemicalData(Connection localConnection, Connection remoteConnection) throws SQLException {
        String localSql = "SELECT * FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code " +
                         "WHERE COALESCE(is_exported, 0) = 0 AND COALESCE(not_process, 0) = 0";

        JavaFXApp.addProcessInfo("=== 开始处理chemical表数据 ===");
        int totalCount = 0;
        int processedCount = 0;
        int exportedCount = 0;
        int skippedCount = 0;
        int errorCount = 0;

        // 首先统计总数
        try (PreparedStatement countStmt = localConnection.prepareStatement(
                "SELECT COUNT(*) FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code " +
                "WHERE COALESCE(is_exported, 0) = 0 AND COALESCE(not_process, 0) = 0")) {
            try (ResultSet countRs = countStmt.executeQuery()) {
                if (countRs.next()) {
                    totalCount = countRs.getInt(1);
                    JavaFXApp.addProcessInfo("待处理记录总数: " + totalCount);
                }
            }
        }

        try (PreparedStatement localPreparedStatement = localConnection.prepareStatement(localSql);
             ResultSet localResultSet = localPreparedStatement.executeQuery()) {

            while (localResultSet.next() && isRunning) {
                String id = localResultSet.getString("id");
                processedCount++;

                JavaFXApp.addProcessInfo("处理记录 [" + processedCount + "/" + totalCount + "] ID: " + id);

                // 检查examine_date是否为空
                if (localResultSet.getDate("examine_date") == null) {
                    markAsNotProcess(localConnection, id);
                    skippedCount++;
                    JavaFXApp.addProcessInfo("  ❌ examine_date为空，标记为not_process，ID: " + id);
                    continue;
                }

                // 检查其他必要字段
                String upperLimit = localResultSet.getString("upper_limit");
                if (upperLimit == null || upperLimit.trim().isEmpty() || "null".equalsIgnoreCase(upperLimit.trim())) {
                    markAsNotProcess(localConnection, id);
                    skippedCount++;
                    JavaFXApp.addProcessInfo("  ❌ upper_limit为空或null，标记为not_process，ID: " + id);
                    continue;
                }

                try {
                    boolean isExported = processChemicalRecord(localResultSet, remoteConnection, localConnection);

                    if (isExported) {
                        markAsExported(localConnection, id);
                        exportedCount++;
                        JavaFXApp.addProcessInfo("  ✅ 成功处理并导出，ID: " + id);
                    } else {
                        markAsNotProcess(localConnection, id);
                        errorCount++;
                        JavaFXApp.addProcessInfo("  ❌ 处理失败，标记为not_process，ID: " + id);
                    }
                } catch (Exception e) {
                    markAsNotProcess(localConnection, id);
                    errorCount++;
                    JavaFXApp.addProcessInfo("  ❌ 处理异常，标记为not_process，ID: " + id + "，错误: " + e.getMessage());
                }

                // 每处理50条记录输出一次统计信息
                if (processedCount % 50 == 0) {
                    JavaFXApp.addProcessInfo("📊 进度统计: 已处理 " + processedCount + "/" + totalCount +
                                           " 条，成功导出 " + exportedCount + " 条，跳过 " + skippedCount + " 条，错误 " + errorCount + " 条");
                }
            }
        } catch (SQLException e) {
            JavaFXApp.addProcessInfo("❌ 处理chemical表数据时发生SQL错误: " + e.getMessage());
            e.printStackTrace();
        }

        JavaFXApp.addProcessInfo("=== chemical表数据处理完成 ===");
        JavaFXApp.addProcessInfo("📈 最终统计: 总计 " + totalCount + " 条记录");
        JavaFXApp.addProcessInfo("  ✅ 成功导出: " + exportedCount + " 条");
        JavaFXApp.addProcessInfo("  ⚠️ 跳过处理: " + skippedCount + " 条");
        JavaFXApp.addProcessInfo("  ❌ 处理错误: " + errorCount + " 条");
        JavaFXApp.addProcessInfo("  📊 处理率: " + String.format("%.2f%%", (double)exportedCount / totalCount * 100));
    }

    // 处理单条chemical记录
    private static boolean processChemicalRecord(ResultSet resultSet, Connection remoteConnection, Connection localConnection) throws SQLException {
        String id = resultSet.getString("id");
        try {
            JavaFXApp.addProcessInfo("    🔄 开始提取数据，ID: " + id);
            String result = extractDataLine(resultSet, remoteConnection, localConnection);

            if (result != null && !result.trim().isEmpty()) {
                JavaFXApp.addProcessInfo("    📝 数据提取成功，开始转换Chemical对象，ID: " + id);
                String examine1 = resultSet.getString("examine1");
                Chemical entity = resultToChemical(result, examine1);

                if (entity != null) {
                    JavaFXApp.addProcessInfo("    💾 Chemical对象创建成功，开始插入chemical_ys表，ID: " + id);
                    insertDataIntoChemicalYsTable(localConnection, entity);
                    JavaFXApp.addProcessInfo("    ✅ 记录处理完成，ID: " + id);
                    return true;
                } else {
                    JavaFXApp.addProcessInfo("    ❌ Chemical对象创建失败，ID: " + id);
                    return false;
                }
            } else {
                JavaFXApp.addProcessInfo("    ❌ 数据提取失败或为空，ID: " + id);
                return false;
            }
        } catch (Exception e) {
            JavaFXApp.addProcessInfo("    ❌ 处理记录时发生异常，ID: " + id + "，错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // 更新数据库，将not_process字段设置为1
    private static void markAsNotProcess(Connection localConnection, String id) throws SQLException {
        String updateSql = "UPDATE dbo.chemical SET not_process = 1 WHERE id = ?";
        try (PreparedStatement updateStmt = localConnection.prepareStatement(updateSql)) {
            updateStmt.setString(1, id);
            updateStmt.executeUpdate();
        }
    }

    private static void markAsExported(Connection connection, String id) throws SQLException {
        String updateSql = "UPDATE dbo.chemical SET is_exported = 1 WHERE id = ?";
        try (PreparedStatement preparedStatement = connection.prepareStatement(updateSql)) {
            preparedStatement.setString(1, id);
            preparedStatement.executeUpdate();
        }
    }





    private static String extractDataLine(ResultSet localResultSet, Connection remoteConnection, Connection localConnection) throws SQLException {
        // 从 resultSet 提取数据并格式化为一行 CSV 字符串
        String id = localResultSet.getString("id");
        String organizationId = localResultSet.getString("organization_id");
        String attributeId = localResultSet.getString("attribute_id");
        String examineDate = localResultSet.getString("examine_date");
        String shift = localResultSet.getString("shift");
        String staff = localResultSet.getString("staff");
        String processSetName = localResultSet.getString("department_code");
        String processName = localResultSet.getString("department_name");
        String productSetName = localResultSet.getString("product_set_name");
        String productName = localResultSet.getString("process_name");
        String testSetName = localResultSet.getString("test_set_name");
        String testName = localResultSet.getString("test_name");
        String sampleSize = localResultSet.getString("sample_size");
        String layerNumber = localResultSet.getString("layer_number");
        String upperLimit = localResultSet.getString("upper_limit");
        String medianSpecification = localResultSet.getString("median_specification");
        String downLimit = localResultSet.getString("down_limit");
        double examine2 = localResultSet.getDouble("examine2");
        String createdBy = localResultSet.getString("created_by");
        String lastUpdatedBy = localResultSet.getString("last_updated_by");
        String creationDate = localResultSet.getString("creation_date");
        String lastUpdateDate = localResultSet.getString("last_update_date");
        String status = localResultSet.getString("status");
        String frequency = localResultSet.getString("frequency");
        String frequencyUnit = localResultSet.getString("frequency_unit");
        String slotBodyName = localResultSet.getString("slot_body_name");
        String projectTeamCode = localResultSet.getString("project_team_code");
        String projectTeamName = localResultSet.getString("project_team_name");
        String testCode = localResultSet.getString("test_code");
        String adjustmentUpperLimit = localResultSet.getString("adjustment_upper_limit");
        String adjustmentMid = localResultSet.getString("adjustment_mid");
        String adjustmentLowerLimit = localResultSet.getString("adjustment_lower_limit");
        String projectUnit = localResultSet.getString("project_unit");
        String departmentName = localResultSet.getString("department_name");
        double examine1 = localResultSet.getDouble("examine1");
        String warningUpperLimit = localResultSet.getString("warning_upper_limit");
        String warningMid = localResultSet.getString("warning_mid");
        String warningLowerLimit = localResultSet.getString("warning_lower_limit");

        // 检查是否存在于rule表中
        boolean existsInRule = existsInRuleTable(localConnection, productName, processName, testName);
        if (existsInRule) {
            StringBuilder csvLine = new StringBuilder();
            csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                    .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                    .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                    .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                    .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                    .append(medianSpecification).append(",").append(downLimit).append(",").append(examine1)
                    .append(",").append(examine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                    .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                    .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                    .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                    .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                    .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
            return csvLine.toString();
        }

        // 获取远程数据库中的 F_PRCS, F_TEST, F_PART 值
        String fTestValue = getF_TESTValueForTestName(remoteConnection, testName);
        if (fTestValue == null) {
            return null;
        }
        String fPrcsValue = getF_PRCSValueForDepartment(remoteConnection, departmentName);
        if (fPrcsValue == null) {
            return null;
        }
        String fPartValue = getF_PARTValueForProductName(remoteConnection, productName);
        if (fPartValue == null) {
            return null;
        }

        // 获取控制限制
        ControlLimits controlLimits = ControlLimitsCache.getControlLimits(remoteConnection, fPrcsValue, fTestValue,
                fPartValue);
//        System.out.println("Control Limits: " + controlLimits.fMean + ", " + controlLimits.fSp);
        if (controlLimits != null) {
            Double adjustedExamine1;
            // 在计算控制值之前
            if ("LV蓬松膨胀剂E".equals(testName)) {
                adjustedExamine1 = handleSpecialCaseForLV(String.valueOf(examine2), examine1);
            } else {
                // 计算控制值
                CalculatedValues calculatedValues = calculateControlValues(controlLimits, testName);
                System.out.println("Calculated Values: " + calculatedValues.lcl1 + ", " + calculatedValues.ucl1);
                adjustedExamine1 = calculateExamineValue(examine1, calculatedValues);
            }
            double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedExamine1 * examine2 / examine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度
            StringBuilder csvLine = new StringBuilder();
            if (!isPaused) {
                csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                        .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                        .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                        .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                        .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                        .append(medianSpecification).append(",").append(downLimit).append(",").append(roundExamineValue(adjustedExamine1))
                        .append(",").append(adjustedExamine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                        .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                        .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                        .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                        .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                        .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
//                System.out.println("CSV Line: " + csvLine.toString());
                return csvLine.toString();
            } else {
                System.out.println("Examine1: " + examine1);
                csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                        .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                        .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                        .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                        .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                        .append(medianSpecification).append(",").append(downLimit).append(",").append(examine1)
                        .append(",").append(examine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                        .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                        .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                        .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                        .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                        .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
                return csvLine.toString();
            }
        }
        return null;
    }

    private static boolean existsInRuleTable(Connection localConnection, String productName, String processName, String testName) throws SQLException {
        String sql = "SELECT COUNT(*) AS count FROM dbo.[rule] WHERE product_name = ? AND process_name = ? AND test_name = ?";
        try (PreparedStatement stmt = localConnection.prepareStatement(sql)) {
            stmt.setString(1, productName);
            stmt.setString(2, processName);
            stmt.setString(3, testName);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("count") > 0;
            }
        }
        return false;
    }

    private static double handleSpecialCaseForLV(String examine2Str, double examine1) {
        double examine2 = Double.parseDouble(examine2Str);
        double[] examine2Options = {14.0, 14.5, 15.0, 16.0};
        double[] examine1Mappings = {166.60, 172.55, 178.50, 190.40};
        double closest = examine2Options[0];
        double diff = Math.abs(examine2 - closest);

        for (int i = 1; i < examine2Options.length; i++) {
            double newDiff = Math.abs(examine2 - examine2Options[i]);
            if (newDiff < diff) {
                closest = examine2Options[i];
                diff = newDiff;
            }
        }

        for (int i = 0; i < examine2Options.length; i++) {
            if (closest == examine2Options[i]) {
                return examine1Mappings[i];
            }
        }

        return examine1; // 默认情况，返回原始 examine1
    }

    private static double roundExamineValue(double value) {
        return Math.round(value * 1000.0) / 1000.0;
    }

    public static void startTask() {
        if (isRunning) {
            return; // 如果任务已经在运行，则不重复启动
        }

        isRunning = true;
        isPaused = false;

        taskThread = new Thread(Main::runMainLogic);
        taskThread.start();
    }

    public static void pauseTask() {
        isPaused = true;
        System.out.println("任务已暂停。");
    }

    public static void stopTask() throws PulsarClientException {
        isRunning = false;
        if (taskThread != null) {
            taskThread.interrupt();
        }
        shutdown();
        System.out.println("任务已停止。");
    }

    public static void stopApp() {
        if (taskThread != null) {
            taskThread.interrupt();
        }
        System.out.println("应用已关闭。");
    }

    private static void initializePulsarClient() {
        try {
            pulsarClient = PulsarClient.builder()
                    .serviceUrl(BROKER_SERVICE_URL)
                    .authentication(AuthenticationFactory.token(TOKEN))
                    .build();
        } catch (PulsarClientException e) {
            e.printStackTrace();
        }
    }

    private static void initializeConsumer() {
        try {
            consumer = pulsarClient.newConsumer(Schema.BYTES)
                    .subscriptionName("G2-Chemical2")
                    .subscriptionType(SubscriptionType.Exclusive)
                    .subscriptionInitialPosition(SubscriptionInitialPosition.Earliest)
                    .topic(MEDICINE_TOPIC)
                    .subscribe();
        } catch (PulsarClientException e) {
            e.printStackTrace();
        }
    }


    public static void runMainLogic() {
        JavaFXApp.addProcessInfo("初始化Pulsar客户端...");
        initializePulsarClient();
        JavaFXApp.addProcessInfo("初始化消费者...");
        initializeConsumer();
        JavaFXApp.addProcessInfo("系统初始化完成，开始定时任务...");

        // 定时任务执行器
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(3);

        // 定时任务：数据读取任务
        Runnable dataReadingTask = () -> {
            JavaFXApp.addProcessInfo("=== 开始数据读取任务 ===");
            int messageCount = 0;
            int successCount = 0;
            int errorCount = 0;

            try (Connection connection = getDatabaseConnection()) {
                JavaFXApp.addProcessInfo("📡 开始监听Pulsar消息队列...");

                while (isRunning) {
                    Message<byte[]> msg = consumer.receive(5, TimeUnit.SECONDS); // 超时5秒
                    if (msg != null) {
                        messageCount++;
                        JavaFXApp.addProcessInfo("📨 接收到新消息 [" + messageCount + "]，消息ID: " + msg.getMessageId());

                        try {
                            // 显示消息内容（前100个字符）
                            String msgContent = new String(msg.getData());
                            String preview = msgContent.length() > 100 ? msgContent.substring(0, 100) + "..." : msgContent;
                            JavaFXApp.addProcessInfo("  📄 消息内容预览: " + preview);

                            processMessage(connection, new ObjectMapper(), msg);
                            consumer.acknowledge(msg);
                            successCount++;
                            JavaFXApp.addProcessInfo("  ✅ 消息处理成功，已确认，消息ID: " + msg.getMessageId());
                        } catch (Exception e) {
                            errorCount++;
                            JavaFXApp.addProcessInfo("  ❌ 消息处理失败，消息ID: " + msg.getMessageId() + "，错误: " + e.getMessage());
                            // 仍然确认消息以避免重复处理
                            consumer.acknowledge(msg);
                        }
                    } else {
                        if (messageCount == 0) {
                            JavaFXApp.addProcessInfo("📭 当前无新数据，等待下次检查");
                        } else {
                            JavaFXApp.addProcessInfo("📭 没有更多消息，本轮处理结束");
                        }
                        break; // 如果没有消息，结束当前循环
                    }
                }
            } catch (Exception e) {
                JavaFXApp.addProcessInfo("❌ 数据读取任务发生错误: " + e.getMessage());
                e.printStackTrace();
            }

            JavaFXApp.addProcessInfo("=== 数据读取任务完成 ===");
            JavaFXApp.addProcessInfo("📈 消息处理统计: 总计 " + messageCount + " 条消息");
            JavaFXApp.addProcessInfo("  ✅ 处理成功: " + successCount + " 条");
            JavaFXApp.addProcessInfo("  ❌ 处理失败: " + errorCount + " 条");
            if (messageCount > 0) {
                JavaFXApp.addProcessInfo("  📊 成功率: " + String.format("%.2f%%", (double)successCount / messageCount * 100));
            }
        };

        // 定时任务：日志读取任务
        Runnable logReadingTask = () -> {
            JavaFXApp.addProcessInfo("=== 开始日志读取任务 ===");
            int totalLogCount = 0;
            int processedLogCount = 0;
            int exportedLogCount = 0;
            int skippedLogCount = 0;
            int errorLogCount = 0;

            try (Connection localConnection = dataSource.getConnection();
                 Connection remoteConnection = dataSourceCloud.getConnection()) {
                printConnectionPoolStats();

                // 首先统计待处理日志总数
                String countLogSql = "SELECT COUNT(*) FROM dbo.chemical_log WHERE processed = 0";
                try (PreparedStatement countStmt = localConnection.prepareStatement(countLogSql);
                     ResultSet countRs = countStmt.executeQuery()) {
                    if (countRs.next()) {
                        totalLogCount = countRs.getInt(1);
                        JavaFXApp.addProcessInfo("待处理日志记录总数: " + totalLogCount);
                    }
                }

                if (totalLogCount == 0) {
                    JavaFXApp.addProcessInfo("没有待处理的日志记录");
                    return;
                }

                String logSql = "SELECT * FROM dbo.chemical_log WHERE processed = 0";
                try (PreparedStatement logStmt = localConnection.prepareStatement(logSql);
                     ResultSet logRs = logStmt.executeQuery()) {

                    while (logRs.next() && isRunning) {
                        String chemicalId = logRs.getString("chemical_id");
                        int logId = logRs.getInt("log_id");
                        processedLogCount++;

                        JavaFXApp.addProcessInfo("处理日志记录 [" + processedLogCount + "/" + totalLogCount + "] log_id: " + logId + ", chemical_id: " + chemicalId);

                        boolean isExported = false;
                        String chemicalSql = "SELECT * FROM dbo.chemical dc JOIN department dept ON dc.department_code = dept.department_code WHERE id = ?";
                        try (PreparedStatement chemicalStmt = localConnection.prepareStatement(chemicalSql)) {
                            chemicalStmt.setString(1, chemicalId);
                            try (ResultSet chemicalRs = chemicalStmt.executeQuery()) {
                                if (chemicalRs.next()) {
                                    // 检查examine_date是否为空
                                    if (chemicalRs.getDate("examine_date") == null) {
                                        markAsNotProcess(localConnection, chemicalId);
                                        skippedLogCount++;
                                        JavaFXApp.addProcessInfo("  ❌ examine_date为空，标记chemical为not_process，chemical_id: " + chemicalId);
                                    } else {
                                        // 检查upper_limit
                                        String upperLimit = chemicalRs.getString("upper_limit");
                                        if (upperLimit == null || upperLimit.trim().isEmpty() || "null".equalsIgnoreCase(upperLimit.trim())) {
                                            markAsNotProcess(localConnection, chemicalId);
                                            skippedLogCount++;
                                            JavaFXApp.addProcessInfo("  ❌ upper_limit为空或null，标记chemical为not_process，chemical_id: " + chemicalId);
                                        } else {
                                            try {
                                                isExported = processChemicalRecord(chemicalRs, remoteConnection, localConnection);
                                                if (isExported) {
                                                    markAsExported(localConnection, chemicalId);
                                                    exportedLogCount++;
                                                    JavaFXApp.addProcessInfo("  ✅ 日志记录处理成功，chemical_id: " + chemicalId);
                                                } else {
                                                    markAsNotProcess(localConnection, chemicalId);
                                                    errorLogCount++;
                                                    JavaFXApp.addProcessInfo("  ❌ 日志记录处理失败，标记chemical为not_process，chemical_id: " + chemicalId);
                                                }
                                            } catch (Exception e) {
                                                markAsNotProcess(localConnection, chemicalId);
                                                errorLogCount++;
                                                JavaFXApp.addProcessInfo("  ❌ 日志记录处理异常，标记chemical为not_process，chemical_id: " + chemicalId + "，错误: " + e.getMessage());
                                            }
                                        }
                                    }
                                } else {
                                    skippedLogCount++;
                                    JavaFXApp.addProcessInfo("  ⚠️ 未找到对应的chemical记录，chemical_id: " + chemicalId);
                                }
                            }
                        }

                        // 更新 log_table 表，将数据标记为已处理
                        String updateLogSql = "UPDATE dbo.chemical_log SET processed = 1 WHERE log_id = ?";
                        try (PreparedStatement updateLogStmt = localConnection.prepareStatement(updateLogSql)) {
                            updateLogStmt.setInt(1, logId);
                            updateLogStmt.executeUpdate();
                            JavaFXApp.addProcessInfo("  📝 日志记录已标记为已处理，log_id: " + logId);
                        }

                        // 每处理20条日志输出一次统计信息
                        if (processedLogCount % 20 == 0) {
                            JavaFXApp.addProcessInfo("📊 日志处理进度: " + processedLogCount + "/" + totalLogCount +
                                                   " 条，成功导出 " + exportedLogCount + " 条，跳过 " + skippedLogCount + " 条，错误 " + errorLogCount + " 条");
                        }
                    }
                }
            } catch (SQLException e) {
                JavaFXApp.addProcessInfo("❌ 日志读取任务发生SQL错误: " + e.getMessage());
                e.printStackTrace();
            }

            JavaFXApp.addProcessInfo("=== 日志读取任务完成 ===");
            JavaFXApp.addProcessInfo("📈 日志处理统计: 总计 " + totalLogCount + " 条日志记录");
            JavaFXApp.addProcessInfo("  ✅ 成功导出: " + exportedLogCount + " 条");
            JavaFXApp.addProcessInfo("  ⚠️ 跳过处理: " + skippedLogCount + " 条");
            JavaFXApp.addProcessInfo("  ❌ 处理错误: " + errorLogCount + " 条");
            if (totalLogCount > 0) {
                JavaFXApp.addProcessInfo("  📊 成功率: " + String.format("%.2f%%", (double)exportedLogCount / totalLogCount * 100));
            }
        };

        // 定时任务：数据处理任务
        Runnable dataProcessingTask = () -> {
            JavaFXApp.addProcessInfo("数据处理任务开始");
            try (Connection localConnection = getDatabaseConnection();
                 Connection remoteConnection = dataSourceCloud.getConnection()) {
                processChemicalData(localConnection, remoteConnection);
            } catch (SQLException e) {
                JavaFXApp.addProcessInfo("数据处理任务发生错误: " + e.getMessage());
                e.printStackTrace();
            }
            JavaFXApp.addProcessInfo("数据处理任务完成");
        };

        // 计划任务顺序执行 - 每30分钟执行一次，减少资源占用
        executorService.scheduleWithFixedDelay(() -> {
            if (isRunning) {
                JavaFXApp.addProcessInfo("开始执行定时任务循环");
                dataReadingTask.run();
                logReadingTask.run();
                dataProcessingTask.run();
                JavaFXApp.addProcessInfo("定时任务循环完成");

                // 强制垃圾回收以减少内存占用
                System.gc();
            }
        }, 0, 30, TimeUnit.MINUTES);

        // 添加JVM关闭钩子以确保资源正确释放
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("正在关闭，释放资源...");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }));
    }



    private static void printConnectionPoolStats() {
        HikariDataSource cloudDataSource = dataSourceCloud;
        // 获取云数据源连接池监控信息
        HikariPoolMXBean cloudPoolMXBean = cloudDataSource.getHikariPoolMXBean();
        int cloudActiveConnections = cloudPoolMXBean.getActiveConnections();
        int cloudIdleConnections = cloudPoolMXBean.getIdleConnections();
        int cloudTotalConnections = cloudPoolMXBean.getTotalConnections();
        System.out.println("Cloud DataSource - Active: " + cloudActiveConnections + ", Idle: " + cloudIdleConnections + ", Total: " + cloudTotalConnections);
    }



    private static Connection getDatabaseConnection() throws SQLException {
        return dataSource.getConnection();
    }

    // 确保正确关闭资源
    public static void shutdown() throws PulsarClientException {
        if (consumer != null) {
            consumer.close();
        }
        if (pulsarClient != null) {
            pulsarClient.close();
        }
        // ... 其他资源关闭 ...
    }

    static class ControlLimitsCache {
        private static final Map<String, ControlLimits> cache = new ConcurrentHashMap<>();

        public static ControlLimits getControlLimits(Connection remoteConnection, String fPrcs, String fTest,
                                                     String fPart) {
            // 构建一个唯一的键来标识每个不同的参数组合
            String key = fPrcs + "_" + fTest + "_" + fPart;

            // 首先尝试从缓存中获取
            if (cache.containsKey(key)) {
                return cache.get(key);
            }

//             如果缓存中没有，则从数据库查询并更新缓存
            if (fPrcs != null && fTest != null && fPart != null) {
                String sql = "SELECT F_MEAN, F_SP FROM dbo.CTRL_LIM WHERE F_PRCS = ? AND F_TEST = ? AND F_PART = ? ORDER BY F_CTRL DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
                try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
                    preparedStatement.setString(1, fPrcs);
                    preparedStatement.setString(2, fTest);
                    preparedStatement.setString(3, fPart);
                    try (ResultSet resultSet = preparedStatement.executeQuery()) {
                        if (resultSet.next()) {
                            double fMean = resultSet.getDouble("F_MEAN");
                            double fSp = resultSet.getDouble("F_SP");
                            ControlLimits limits = new ControlLimits(fMean, fSp);
                            cache.put(key, limits); // 更新缓存
                            return limits;
                        }
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }
    }

    private static class ControlLimits {
        double fMean;
        double fSp;

        public ControlLimits(double fMean, double fSp) {
            this.fMean = fMean;
            this.fSp = fSp;
        }
    }

    private static class CalculatedValues {
        double upperControlLimit;
        double lowerControlLimit;
        double lcl1;
        double ucl1;

        public CalculatedValues(double upperControlLimit, double lowerControlLimit, double lcl1, double ucl1) {
            this.upperControlLimit = upperControlLimit;
            this.lowerControlLimit = lowerControlLimit;
            this.lcl1 = lcl1;
            this.ucl1 = ucl1;
        }
    }

    private static void insertDataIntoChemicalYsTable(Connection connection, Chemical entity) {
        String id = entity.getId();

        // 详细验证所有关键字段
        JavaFXApp.addProcessInfo("      🔍 开始验证数据字段，ID: " + id);

        // 验证upper_limit字段
        if (entity.getUpperLimit() == null ||
            entity.getUpperLimit().trim().isEmpty() ||
            "null".equalsIgnoreCase(entity.getUpperLimit().trim())) {
            JavaFXApp.addProcessInfo("      ❌ upper_limit验证失败：值为空或null，ID: " + id + "，值: " + entity.getUpperLimit());
            throw new RuntimeException("upper_limit字段无效");
        }

        // 验证其他关键字段
        if (entity.getExamineDate() == null) {
            JavaFXApp.addProcessInfo("      ❌ examine_date验证失败：值为null，ID: " + id);
            throw new RuntimeException("examine_date字段无效");
        }

        if (entity.getProductName() == null || entity.getProductName().trim().isEmpty()) {
            JavaFXApp.addProcessInfo("      ❌ product_name验证失败：值为空，ID: " + id);
            throw new RuntimeException("product_name字段无效");
        }

        if (entity.getProcessName() == null || entity.getProcessName().trim().isEmpty()) {
            JavaFXApp.addProcessInfo("      ❌ process_name验证失败：值为空，ID: " + id);
            throw new RuntimeException("process_name字段无效");
        }

        if (entity.getTestName() == null || entity.getTestName().trim().isEmpty()) {
            JavaFXApp.addProcessInfo("      ❌ test_name验证失败：值为空，ID: " + id);
            throw new RuntimeException("test_name字段无效");
        }

        JavaFXApp.addProcessInfo("      ✅ 数据字段验证通过，ID: " + id);

        // 创建 SQL 插入语句，根据表列名进行调整
        String sql = "INSERT INTO dbo.chemical_ys (id, organization_id, attribute_id, examine_date, shift, staff, " +
                "process_set_name, process_name, product_set_name, product_name, test_set_name, " +
                "test_name, sample_size, layer_number, upper_limit, median_specification, down_limit, examine1, " +
                "examine2, created_by, last_updated_by, creation_date, last_update_date, status, frequency," +
                " " +
                "frequency_unit, slot_body_name, project_team_code, project_team_name, test_code, " +
                "adjustment_upper_limit, " +
                "adjustment_mid, adjustment_lower_limit, project_unit,insertion_time,warning_upper_limit," +
                "warning_mid,warning_lower_limit,examine1_zs) VALUES (?, ?, ?, ?, ?, ?," +
                " ?, ?, ?, ?," +
                " ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?)";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, entity.getId());
            setNullableLong(preparedStatement, 2, entity.getOrganizationId());
            setNullableLong(preparedStatement, 3, entity.getAttributeId());
            preparedStatement.setTimestamp(4, entity.getExamineDate());
            preparedStatement.setString(5, entity.getShift());
            preparedStatement.setString(6, entity.getStaff());
            preparedStatement.setString(7, entity.getProcessSetName());
            preparedStatement.setString(8, entity.getProcessName());
            preparedStatement.setString(9, entity.getProductSetName());
            preparedStatement.setString(10, entity.getProductName());
            preparedStatement.setString(11, entity.getTestSetName());
            preparedStatement.setString(12, entity.getTestName());
            preparedStatement.setString(13, entity.getSampleSize());
            preparedStatement.setString(14, entity.getLayerNumber());
            preparedStatement.setString(15, entity.getUpperLimit());
            preparedStatement.setString(16, entity.getMedianSpecification());
            preparedStatement.setString(17, entity.getDownLimit());
            preparedStatement.setString(18, entity.getExamine1YS());
            preparedStatement.setString(19, entity.getExamine2());
            preparedStatement.setString(20, entity.getCreatedBy());
            preparedStatement.setString(21, entity.getLastUpdatedBy());
            preparedStatement.setTimestamp(22, entity.getCreationDate());
            preparedStatement.setTimestamp(23, entity.getLastUpdateDate());
            setNullableLong(preparedStatement, 24, entity.getStatus());
            preparedStatement.setString(25, entity.getFrequency());
            preparedStatement.setString(26, entity.getFrequencyUnit());
            preparedStatement.setString(27, entity.getSlotBodyName());
            preparedStatement.setString(28, entity.getProjectTeamCode());
            preparedStatement.setString(29, entity.getProjectTeamName());
            preparedStatement.setString(30, entity.getTestCode());
            preparedStatement.setString(31, entity.getAdjustmentUpperLimit());
            preparedStatement.setString(32, entity.getAdjustmentMid());
            preparedStatement.setString(33, entity.getAdjustmentLowerLimit());
            preparedStatement.setString(34, entity.getProjectUnit());
            preparedStatement.setTimestamp(35, new Timestamp(System.currentTimeMillis()));
            preparedStatement.setString(36, entity.getWarningUpperLimit());
            preparedStatement.setString(37, entity.getWarningMid());
            preparedStatement.setString(38, entity.getWarningLowerLimit());
            preparedStatement.setString(39, entity.getExamine1());
            JavaFXApp.addProcessInfo("      💾 开始执行SQL插入，ID: " + id);
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                JavaFXApp.addProcessInfo("      ✅ 成功插入chemical_ys表，ID: " + id + "，影响行数: " + rowsInserted);
            } else {
                JavaFXApp.addProcessInfo("      ⚠️ 插入chemical_ys表无影响行数，ID: " + id);
            }
        } catch (SQLException e) {
            JavaFXApp.addProcessInfo("      ❌ 插入chemical_ys表SQL异常，ID: " + id + "，错误代码: " + e.getErrorCode() + "，错误信息: " + e.getMessage());
            handleSQLException(e);
            throw new RuntimeException("数据库插入失败: " + e.getMessage(), e);
        } catch (Exception e) {
            JavaFXApp.addProcessInfo("      ❌ 插入chemical_ys表发生未知异常，ID: " + id + "，错误: " + e.getMessage());
            throw new RuntimeException("插入过程发生异常: " + e.getMessage(), e);
        }
    }

    public static Chemical resultToChemical(String result, String examine1) {
        if (result == null) {
            return null;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String[] parts = result.split(",", -1); // 使用-1保留空值
        Chemical chemical = new Chemical();

        try {
            chemical.setId(parts[0]);
            chemical.setOrganizationId(Long.valueOf(parts[1]));
            chemical.setAttributeId(Long.valueOf(parts[2]));
            chemical.setExamineDate(new Timestamp(dateFormat.parse(parts[3]).getTime()));
            chemical.setShift(parts[4]);
            chemical.setStaff(parts[5]);
            chemical.setProcessSetName(parts[6]);
            chemical.setProcessName(parts[7]);
            chemical.setProductSetName(parts[8]);
            chemical.setProductName(parts[9]);
            chemical.setTestSetName(parts[10]);
            chemical.setTestName(parts[11]);
            chemical.setSampleSize(parts[12]);
            chemical.setLayerNumber(parts[13]);
            chemical.setUpperLimit(parts[14]);
            chemical.setMedianSpecification(parts[15]);
            chemical.setDownLimit(parts[16]);
            chemical.setExamine1YS(parts[17]);
            chemical.setExamine2(parts[18]);
            chemical.setCreatedBy(parts[19]);
            chemical.setLastUpdatedBy(parts[20]);
            chemical.setCreationDate(new Timestamp(dateFormat.parse(parts[21]).getTime()));
            chemical.setLastUpdateDate(new Timestamp(dateFormat.parse(parts[22]).getTime()));
            chemical.setStatus(Long.parseLong(parts[23]));
            chemical.setFrequency(parts[24]);
            chemical.setFrequencyUnit(parts[25]);
            chemical.setSlotBodyName(parts[26]);
            chemical.setProjectTeamCode(parts[27]);
            chemical.setProjectTeamName(parts[28]);
            chemical.setTestCode(parts[29]);
            chemical.setAdjustmentUpperLimit(parts[30]);
            chemical.setAdjustmentMid(parts[31]);
            chemical.setAdjustmentLowerLimit(parts[32]);
            chemical.setProjectUnit(parts[33]);
            chemical.setWarningUpperLimit(parts[34]);
            chemical.setWarningMid(parts[35]);
            chemical.setWarningLowerLimit(parts[36]);
            chemical.setExamine1(examine1);
            return chemical;
        } catch (Exception e) {
            JavaFXApp.addProcessInfo("解析Chemical对象时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
