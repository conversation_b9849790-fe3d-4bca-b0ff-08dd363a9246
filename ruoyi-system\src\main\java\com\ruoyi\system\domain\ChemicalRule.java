package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 化学处理规则对象 chemical_rule
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ChemicalRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long ruleId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 工艺名称 */
    @Excel(name = "工艺名称")
    private String processName;

    /** 测试名称 */
    @Excel(name = "测试名称")
    private String testName;

    /** 是否刷新 */
    @Excel(name = "是否刷新")
    private Boolean isRefresh;

    /** 规则类型 */
    @Excel(name = "规则类型", dictType = "chemical_rule_type")
    private String ruleType;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String ruleDesc;

    /** 规则配置 */
    private String ruleConfig;

    /** 状态 */
    @Excel(name = "状态", dictType = "sys_normal_disable")
    private String status;

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    public void setProcessName(String processName) 
    {
        this.processName = processName;
    }

    public String getProcessName() 
    {
        return processName;
    }

    public void setTestName(String testName) 
    {
        this.testName = testName;
    }

    public String getTestName() 
    {
        return testName;
    }

    public void setIsRefresh(Boolean isRefresh) 
    {
        this.isRefresh = isRefresh;
    }

    public Boolean getIsRefresh() 
    {
        return isRefresh;
    }

    public void setRuleType(String ruleType) 
    {
        this.ruleType = ruleType;
    }

    public String getRuleType() 
    {
        return ruleType;
    }

    public void setRuleDesc(String ruleDesc) 
    {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleDesc() 
    {
        return ruleDesc;
    }

    public void setRuleConfig(String ruleConfig) 
    {
        this.ruleConfig = ruleConfig;
    }

    public String getRuleConfig() 
    {
        return ruleConfig;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleId", getRuleId())
            .append("productName", getProductName())
            .append("processName", getProcessName())
            .append("testName", getTestName())
            .append("isRefresh", getIsRefresh())
            .append("ruleType", getRuleType())
            .append("ruleDesc", getRuleDesc())
            .append("ruleConfig", getRuleConfig())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
