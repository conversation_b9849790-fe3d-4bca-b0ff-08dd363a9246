package org.example;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.scene.Scene;
import javafx.stage.DirectoryChooser;
import org.apache.pulsar.client.api.PulsarClientException;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.example.Main.*;

public class JavaFXApp extends Application {

    private static Label statusLabel; // 将状态标签设为静态变量

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("G2药水应审软件");

        statusLabel = new Label("当前状态：等待开始"); // 初始化状态标签
        // 创建按钮和设置首选大小
        Button refreshButton = new Button("刷新数据");
        Button exportButton = new Button("导出数据");
        Button choosePathButton = new Button("选择保存路径");
        Button chooseBackupPathButton = new Button("选择备份路径");


        // —— 时间筛选区开始 —— //
        DatePicker startDatePicker = new DatePicker(LocalDate.now());
        DatePicker endDatePicker   = new DatePicker(LocalDate.now());

        // 可编辑的 Spinner：时（0–23）
        Spinner<Integer> startHour = new Spinner<>(0, 23, 0);
        startHour.setPrefWidth(60);
        startHour.setEditable(true);

        // 可编辑的 Spinner：分（0–59）
        Spinner<Integer> startMinute = new Spinner<>(0, 59, 0);
        startMinute.setPrefWidth(60);
        startMinute.setEditable(true);

        Spinner<Integer> endHour = new Spinner<>(0, 23, 23);
        endHour.setPrefWidth(60);
        endHour.setEditable(true);

        Spinner<Integer> endMinute = new Spinner<>(0, 59, 59);
        endMinute.setPrefWidth(60);
        endMinute.setEditable(true);

        HBox datePickersBox = new HBox(8,
                new Label("开始："), startDatePicker,
                new Label("时"),     startHour,
                new Label("分"),     startMinute,
                new Label("结束："), endDatePicker,
                new Label("时"),     endHour,
                new Label("分"),     endMinute
        );
        datePickersBox.setAlignment(Pos.CENTER);
        datePickersBox.setPadding(new Insets(10));
        startDatePicker.setPrefSize(120, 30);
        endDatePicker.setPrefSize(120, 30);
        startDatePicker.setValue(LocalDate.now());
        endDatePicker.setValue(LocalDate.now());

        // 设置文本域和标签的首选大小和样式
        TextField pathTextField = new TextField(saveFilePath);
        TextField pathTextField2 = new TextField();


        Label pathLabel = new Label("保存路径：");
        Label pathLabel2 = new Label("备份路径：");

        // 设置按钮容器的布局
        HBox buttonsBox = new HBox(50, refreshButton, exportButton);
        buttonsBox.setAlignment(Pos.CENTER);

        // 设置路径文本域容器的布局
        HBox pathBox = new HBox(20, pathLabel, pathTextField, choosePathButton);
        pathBox.setAlignment(Pos.CENTER);

        HBox pathBox2 = new HBox(20, pathLabel2, pathTextField2, chooseBackupPathButton);
        pathBox2.setAlignment(Pos.CENTER);
        // 创建状态显示标签
        statusLabel.setFont(new Font(16)); // 设置字体大小

        // 将所有容器添加到主容器
        VBox mainBox = new VBox(30, buttonsBox, pathBox, pathBox2, datePickersBox, statusLabel);
        mainBox.setAlignment(Pos.CENTER);
        mainBox.setPadding(new Insets(30, 30, 30, 30));

        // 设置场景和显示舞台
        Scene scene = new Scene(mainBox, 1000, 400);
        scene.getStylesheets().add(getClass().getResource("/style.css").toExternalForm());
        primaryStage.setScene(scene);
        primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/app_icon.png")));
        primaryStage.show();

        // 刷新数据按钮的事件处理
        refreshButton.setOnAction(event -> {
            // 重置所有状态标志
            resetAllFlags();
            isRefresh = true;
            statusLabel.setText("当前状态：正在刷新数据...");

            // 在新线程中执行，避免阻塞UI
            new Thread(() -> {
                try {
                    Main.runMainLogic();
                    Platform.runLater(() -> statusLabel.setText("当前状态：数据刷新完成"));
                } catch (Exception e) {
                    Platform.runLater(() -> statusLabel.setText("当前状态：数据刷新失败 - " + e.getMessage()));
                    e.printStackTrace();
                }
            }).start();
        });


        // 导出数据按钮的事件处理
        exportButton.setOnAction(event -> {
            if (!isPathSelected()) {
                // 如果路径没有选择，显示警告信息
                Alert alert = new Alert(Alert.AlertType.WARNING, "请先选择一个保存路径。", ButtonType.OK);
                alert.showAndWait();
                statusLabel.setText("当前状态：等待路径选择");
            } else {
                try {
                    // 重置所有状态标志
                    resetAllFlags();
                    isDataExport = true;

                    // 读取日期和时间
                    LocalDate sd = startDatePicker.getValue();
                    LocalDate ed = endDatePicker.getValue();
                    int sh = startHour.getValue();
                    int sm = startMinute.getValue();
                    int eh = endHour.getValue();
                    int em = endMinute.getValue();

                    // 构造精确到分钟的 LocalDateTime，秒默认为0和59
                    LocalDateTime startDT = sd.atTime(sh, sm, 0);
                    LocalDateTime endDT = ed.atTime(eh, em, 59);

                    // 传给后端
                    Main.setProcessingDates(startDT, endDT);
                    statusLabel.setText("当前状态：正在导出数据...");

                    // 在新线程中执行，避免阻塞UI
                    new Thread(() -> {
                        try {
                            Main.runMainLogic();
                            Platform.runLater(() -> statusLabel.setText("当前状态：数据导出完成"));
                        } catch (Exception e) {
                            Platform.runLater(() -> statusLabel.setText("当前状态：数据导出失败 - " + e.getMessage()));
                            e.printStackTrace();
                        }
                    }).start();

                } catch (Exception ex) {
                    new Alert(Alert.AlertType.ERROR, "请输入合法的时（0-23）和分（0-59）。", ButtonType.OK).showAndWait();
                }
            }
        });

        // 路径选择按钮的事件处理
        choosePathButton.setOnAction(event -> {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            File selectedDirectory = directoryChooser.showDialog(primaryStage);

            if (selectedDirectory != null) {
                pathTextField.setText(selectedDirectory.getAbsolutePath());
            }
            saveFilePath = selectedDirectory.getAbsolutePath();
        });

        // 备份路径选择按钮的事件处理
        chooseBackupPathButton.setOnAction(event -> {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            File selectedDirectory = directoryChooser.showDialog(primaryStage);

            if (selectedDirectory != null) {
                pathTextField2.setText(selectedDirectory.getAbsolutePath());
            }
            backupFilePath = selectedDirectory.getAbsolutePath();
        });


    }

    private boolean isPathSelected() {
        return saveFilePath != null && !saveFilePath.trim().isEmpty();
    }

    // 重置所有状态标志的方法
    private void resetAllFlags() {
        isRefresh = false;
        isDataExport = false;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
