package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ChemicalRule;

/**
 * 化学处理规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ChemicalRuleMapper 
{
    /**
     * 查询化学处理规则
     * 
     * @param ruleId 化学处理规则主键
     * @return 化学处理规则
     */
    public ChemicalRule selectChemicalRuleByRuleId(Long ruleId);

    /**
     * 查询化学处理规则列表
     * 
     * @param chemicalRule 化学处理规则
     * @return 化学处理规则集合
     */
    public List<ChemicalRule> selectChemicalRuleList(ChemicalRule chemicalRule);

    /**
     * 新增化学处理规则
     * 
     * @param chemicalRule 化学处理规则
     * @return 结果
     */
    public int insertChemicalRule(ChemicalRule chemicalRule);

    /**
     * 修改化学处理规则
     * 
     * @param chemicalRule 化学处理规则
     * @return 结果
     */
    public int updateChemicalRule(ChemicalRule chemicalRule);

    /**
     * 删除化学处理规则
     * 
     * @param ruleId 化学处理规则主键
     * @return 结果
     */
    public int deleteChemicalRuleByRuleId(Long ruleId);

    /**
     * 批量删除化学处理规则
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChemicalRuleByRuleIds(Long[] ruleIds);

    /**
     * 根据产品、工艺、测试名称查询规则
     * 
     * @param productName 产品名称
     * @param processName 工艺名称
     * @param testName 测试名称
     * @return 化学处理规则
     */
    public ChemicalRule selectRuleByNames(String productName, String processName, String testName);

    /**
     * 检查规则是否存在
     * 
     * @param productName 产品名称
     * @param processName 工艺名称
     * @param testName 测试名称
     * @return 是否存在
     */
    public boolean existsRule(String productName, String processName, String testName);

    /**
     * 查询启用的规则列表
     * 
     * @return 化学处理规则集合
     */
    public List<ChemicalRule> selectEnabledRuleList();

    /**
     * 查询刷新规则列表
     * 
     * @return 化学处理规则集合
     */
    public List<ChemicalRule> selectRefreshRuleList();

    /**
     * 查询特殊规则列表
     * 
     * @return 化学处理规则集合
     */
    public List<ChemicalRule> selectSpecialRuleList();

    /**
     * 统计规则数量
     * 
     * @param chemicalRule 查询条件
     * @return 规则数量
     */
    public int countRules(ChemicalRule chemicalRule);
}
