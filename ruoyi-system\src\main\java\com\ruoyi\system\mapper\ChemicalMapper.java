package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.Chemical;

/**
 * 化学检测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ChemicalMapper 
{
    /**
     * 查询化学检测数据
     * 
     * @param id 化学检测数据主键
     * @return 化学检测数据
     */
    public Chemical selectChemicalById(String id);

    /**
     * 查询化学检测数据列表
     * 
     * @param chemical 化学检测数据
     * @return 化学检测数据集合
     */
    public List<Chemical> selectChemicalList(Chemical chemical);

    /**
     * 新增化学检测数据
     * 
     * @param chemical 化学检测数据
     * @return 结果
     */
    public int insertChemical(Chemical chemical);

    /**
     * 修改化学检测数据
     * 
     * @param chemical 化学检测数据
     * @return 结果
     */
    public int updateChemical(Chemical chemical);

    /**
     * 删除化学检测数据
     * 
     * @param id 化学检测数据主键
     * @return 结果
     */
    public int deleteChemicalById(String id);

    /**
     * 批量删除化学检测数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChemicalByIds(String[] ids);

    /**
     * 查询未导出的化学检测数据
     * 
     * @param chemical 查询条件
     * @return 化学检测数据集合
     */
    public List<Chemical> selectUnexportedChemicalList(Chemical chemical);

    /**
     * 查询未处理的化学检测数据
     * 
     * @param chemical 查询条件
     * @return 化学检测数据集合
     */
    public List<Chemical> selectUnprocessedChemicalList(Chemical chemical);

    /**
     * 批量更新导出状态
     * 
     * @param ids 需要更新的数据主键集合
     * @return 结果
     */
    public int updateExportedStatusByIds(String[] ids);

    /**
     * 批量更新处理状态
     * 
     * @param ids 需要更新的数据主键集合
     * @return 结果
     */
    public int updateProcessedStatusByIds(String[] ids);

    /**
     * 标记为不处理
     * 
     * @param id 数据主键
     * @return 结果
     */
    public int markAsNotProcess(String id);

    /**
     * 标记为已导出
     * 
     * @param id 数据主键
     * @return 结果
     */
    public int markAsExported(String id);

    /**
     * 重置处理状态
     * 
     * @param id 数据主键
     * @return 结果
     */
    public int resetProcessStatus(String id);

    /**
     * 统计数据
     * 
     * @param chemical 查询条件
     * @return 统计结果
     */
    public int countChemicalData(Chemical chemical);

    /**
     * 统计未处理数据
     * 
     * @return 未处理数据数量
     */
    public int countUnprocessedData();

    /**
     * 统计已导出数据
     * 
     * @return 已导出数据数量
     */
    public int countExportedData();

    /**
     * 统计今日新增数据
     * 
     * @return 今日新增数据数量
     */
    public int countTodayNewData();
}
