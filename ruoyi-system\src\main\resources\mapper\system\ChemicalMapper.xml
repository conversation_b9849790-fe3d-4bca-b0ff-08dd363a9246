<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ChemicalMapper">
    
    <resultMap type="Chemical" id="ChemicalResult">
        <result property="id"    column="id"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="attributeId"    column="attribute_id"    />
        <result property="examineDate"    column="examine_date"    />
        <result property="shift"    column="shift"    />
        <result property="staff"    column="staff"    />
        <result property="departmentCode"    column="department_code"    />
        <result property="processSetName"    column="process_set_name"    />
        <result property="processName"    column="process_name"    />
        <result property="productSetName"    column="product_set_name"    />
        <result property="productName"    column="product_name"    />
        <result property="testSetName"    column="test_set_name"    />
        <result property="testName"    column="test_name"    />
        <result property="sampleSize"    column="sample_size"    />
        <result property="layerNumber"    column="layer_number"    />
        <result property="upperLimit"    column="upper_limit"    />
        <result property="medianSpecification"    column="median_specification"    />
        <result property="downLimit"    column="down_limit"    />
        <result property="examine1"    column="examine1"    />
        <result property="examine1Ys"    column="examine1_ys"    />
        <result property="examine2"    column="examine2"    />
        <result property="createdBy"    column="created_by"    />
        <result property="lastUpdatedBy"    column="last_updated_by"    />
        <result property="creationDate"    column="creation_date"    />
        <result property="lastUpdateDate"    column="last_update_date"    />
        <result property="status"    column="status"    />
        <result property="frequency"    column="frequency"    />
        <result property="frequencyUnit"    column="frequency_unit"    />
        <result property="slotBodyName"    column="slot_body_name"    />
        <result property="projectTeamCode"    column="project_team_code"    />
        <result property="projectTeamName"    column="project_team_name"    />
        <result property="testCode"    column="test_code"    />
        <result property="adjustmentUpperLimit"    column="adjustment_upper_limit"    />
        <result property="adjustmentMid"    column="adjustment_mid"    />
        <result property="adjustmentLowerLimit"    column="adjustment_lower_limit"    />
        <result property="projectUnit"    column="project_unit"    />
        <result property="insertionTime"    column="insertion_time"    />
        <result property="isExported"    column="is_exported"    />
        <result property="notProcess"    column="not_process"    />
        <result property="warningUpperLimit"    column="warning_upper_limit"    />
        <result property="warningMid"    column="warning_mid"    />
        <result property="warningLowerLimit"    column="warning_lower_limit"    />
        <result property="attribute1"    column="attribute1"    />
        <result property="attribute2"    column="attribute2"    />
        <result property="attribute3"    column="attribute3"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="departmentName"    column="department_name"    />
    </resultMap>

    <sql id="selectChemicalVo">
        select c.id, c.organization_id, c.attribute_id, c.examine_date, c.shift, c.staff, 
               c.department_code, c.process_set_name, c.process_name, c.product_set_name, 
               c.product_name, c.test_set_name, c.test_name, c.sample_size, c.layer_number, 
               c.upper_limit, c.median_specification, c.down_limit, c.examine1, c.examine1_ys, 
               c.examine2, c.created_by, c.last_updated_by, c.creation_date, c.last_update_date, 
               c.status, c.frequency, c.frequency_unit, c.slot_body_name, c.project_team_code, 
               c.project_team_name, c.test_code, c.adjustment_upper_limit, c.adjustment_mid, 
               c.adjustment_lower_limit, c.project_unit, c.insertion_time, c.is_exported, 
               c.not_process, c.warning_upper_limit, c.warning_mid, c.warning_lower_limit, 
               c.attribute1, c.attribute2, c.attribute3, c.remark, c.create_time, c.update_time,
               d.department_name
        from chemical c
        left join chemical_department d on c.department_code = d.department_code
    </sql>

    <select id="selectChemicalList" parameterType="Chemical" resultMap="ChemicalResult">
        <include refid="selectChemicalVo"/>
        <where>  
            <if test="id != null  and id != ''"> and c.id = #{id}</if>
            <if test="organizationId != null "> and c.organization_id = #{organizationId}</if>
            <if test="attributeId != null "> and c.attribute_id = #{attributeId}</if>
            <if test="examineDate != null "> and c.examine_date = #{examineDate}</if>
            <if test="shift != null  and shift != ''"> and c.shift = #{shift}</if>
            <if test="staff != null  and staff != ''"> and c.staff like concat('%', #{staff}, '%')</if>
            <if test="departmentCode != null  and departmentCode != ''"> and c.department_code = #{departmentCode}</if>
            <if test="processName != null  and processName != ''"> and c.process_name like concat('%', #{processName}, '%')</if>
            <if test="productName != null  and productName != ''"> and c.product_name like concat('%', #{productName}, '%')</if>
            <if test="testName != null  and testName != ''"> and c.test_name like concat('%', #{testName}, '%')</if>
            <if test="layerNumber != null  and layerNumber != ''"> and c.layer_number = #{layerNumber}</if>
            <if test="status != null "> and c.status = #{status}</if>
            <if test="isExported != null "> and c.is_exported = #{isExported}</if>
            <if test="notProcess != null "> and c.not_process = #{notProcess}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(c.examine_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(c.examine_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by c.examine_date desc
    </select>
    
    <select id="selectChemicalById" parameterType="String" resultMap="ChemicalResult">
        <include refid="selectChemicalVo"/>
        where c.id = #{id}
    </select>

    <select id="selectUnexportedChemicalList" parameterType="Chemical" resultMap="ChemicalResult">
        <include refid="selectChemicalVo"/>
        where (c.is_exported = 0 or c.is_exported is null) 
          and (c.not_process = 0 or c.not_process is null)
        <if test="examineDate != null "> and c.examine_date = #{examineDate}</if>
        <if test="departmentCode != null  and departmentCode != ''"> and c.department_code = #{departmentCode}</if>
        <if test="processName != null  and processName != ''"> and c.process_name like concat('%', #{processName}, '%')</if>
        <if test="productName != null  and productName != ''"> and c.product_name like concat('%', #{productName}, '%')</if>
        <if test="testName != null  and testName != ''"> and c.test_name like concat('%', #{testName}, '%')</if>
        order by c.examine_date desc
    </select>

    <select id="selectUnprocessedChemicalList" parameterType="Chemical" resultMap="ChemicalResult">
        <include refid="selectChemicalVo"/>
        where (c.is_exported = 0 or c.is_exported is null) 
          and (c.not_process = 0 or c.not_process is null)
          and c.upper_limit is not null 
          and c.upper_limit != '' 
          and c.upper_limit != 'null'
          and c.examine_date is not null
        <if test="departmentCode != null  and departmentCode != ''"> and c.department_code = #{departmentCode}</if>
        order by c.examine_date desc
    </select>

    <insert id="insertChemical" parameterType="Chemical">
        insert into chemical
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="organizationId != null">organization_id,</if>
            <if test="attributeId != null">attribute_id,</if>
            <if test="examineDate != null">examine_date,</if>
            <if test="shift != null">shift,</if>
            <if test="staff != null">staff,</if>
            <if test="departmentCode != null">department_code,</if>
            <if test="processSetName != null">process_set_name,</if>
            <if test="processName != null">process_name,</if>
            <if test="productSetName != null">product_set_name,</if>
            <if test="productName != null">product_name,</if>
            <if test="testSetName != null">test_set_name,</if>
            <if test="testName != null">test_name,</if>
            <if test="sampleSize != null">sample_size,</if>
            <if test="layerNumber != null">layer_number,</if>
            <if test="upperLimit != null">upper_limit,</if>
            <if test="medianSpecification != null">median_specification,</if>
            <if test="downLimit != null">down_limit,</if>
            <if test="examine1 != null">examine1,</if>
            <if test="examine1Ys != null">examine1_ys,</if>
            <if test="examine2 != null">examine2,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="lastUpdatedBy != null">last_updated_by,</if>
            <if test="creationDate != null">creation_date,</if>
            <if test="lastUpdateDate != null">last_update_date,</if>
            <if test="status != null">status,</if>
            <if test="frequency != null">frequency,</if>
            <if test="frequencyUnit != null">frequency_unit,</if>
            <if test="slotBodyName != null">slot_body_name,</if>
            <if test="projectTeamCode != null">project_team_code,</if>
            <if test="projectTeamName != null">project_team_name,</if>
            <if test="testCode != null">test_code,</if>
            <if test="adjustmentUpperLimit != null">adjustment_upper_limit,</if>
            <if test="adjustmentMid != null">adjustment_mid,</if>
            <if test="adjustmentLowerLimit != null">adjustment_lower_limit,</if>
            <if test="projectUnit != null">project_unit,</if>
            <if test="insertionTime != null">insertion_time,</if>
            <if test="isExported != null">is_exported,</if>
            <if test="notProcess != null">not_process,</if>
            <if test="warningUpperLimit != null">warning_upper_limit,</if>
            <if test="warningMid != null">warning_mid,</if>
            <if test="warningLowerLimit != null">warning_lower_limit,</if>
            <if test="attribute1 != null">attribute1,</if>
            <if test="attribute2 != null">attribute2,</if>
            <if test="attribute3 != null">attribute3,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="organizationId != null">#{organizationId},</if>
            <if test="attributeId != null">#{attributeId},</if>
            <if test="examineDate != null">#{examineDate},</if>
            <if test="shift != null">#{shift},</if>
            <if test="staff != null">#{staff},</if>
            <if test="departmentCode != null">#{departmentCode},</if>
            <if test="processSetName != null">#{processSetName},</if>
            <if test="processName != null">#{processName},</if>
            <if test="productSetName != null">#{productSetName},</if>
            <if test="productName != null">#{productName},</if>
            <if test="testSetName != null">#{testSetName},</if>
            <if test="testName != null">#{testName},</if>
            <if test="sampleSize != null">#{sampleSize},</if>
            <if test="layerNumber != null">#{layerNumber},</if>
            <if test="upperLimit != null">#{upperLimit},</if>
            <if test="medianSpecification != null">#{medianSpecification},</if>
            <if test="downLimit != null">#{downLimit},</if>
            <if test="examine1 != null">#{examine1},</if>
            <if test="examine1Ys != null">#{examine1Ys},</if>
            <if test="examine2 != null">#{examine2},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="lastUpdatedBy != null">#{lastUpdatedBy},</if>
            <if test="creationDate != null">#{creationDate},</if>
            <if test="lastUpdateDate != null">#{lastUpdateDate},</if>
            <if test="status != null">#{status},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="frequencyUnit != null">#{frequencyUnit},</if>
            <if test="slotBodyName != null">#{slotBodyName},</if>
            <if test="projectTeamCode != null">#{projectTeamCode},</if>
            <if test="projectTeamName != null">#{projectTeamName},</if>
            <if test="testCode != null">#{testCode},</if>
            <if test="adjustmentUpperLimit != null">#{adjustmentUpperLimit},</if>
            <if test="adjustmentMid != null">#{adjustmentMid},</if>
            <if test="adjustmentLowerLimit != null">#{adjustmentLowerLimit},</if>
            <if test="projectUnit != null">#{projectUnit},</if>
            <if test="insertionTime != null">#{insertionTime},</if>
            <if test="isExported != null">#{isExported},</if>
            <if test="notProcess != null">#{notProcess},</if>
            <if test="warningUpperLimit != null">#{warningUpperLimit},</if>
            <if test="warningMid != null">#{warningMid},</if>
            <if test="warningLowerLimit != null">#{warningLowerLimit},</if>
            <if test="attribute1 != null">#{attribute1},</if>
            <if test="attribute2 != null">#{attribute2},</if>
            <if test="attribute3 != null">#{attribute3},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateChemical" parameterType="Chemical">
        update chemical
        <trim prefix="SET" suffixOverrides=",">
            <if test="organizationId != null">organization_id = #{organizationId},</if>
            <if test="attributeId != null">attribute_id = #{attributeId},</if>
            <if test="examineDate != null">examine_date = #{examineDate},</if>
            <if test="shift != null">shift = #{shift},</if>
            <if test="staff != null">staff = #{staff},</if>
            <if test="departmentCode != null">department_code = #{departmentCode},</if>
            <if test="processSetName != null">process_set_name = #{processSetName},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="productSetName != null">product_set_name = #{productSetName},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="testSetName != null">test_set_name = #{testSetName},</if>
            <if test="testName != null">test_name = #{testName},</if>
            <if test="sampleSize != null">sample_size = #{sampleSize},</if>
            <if test="layerNumber != null">layer_number = #{layerNumber},</if>
            <if test="upperLimit != null">upper_limit = #{upperLimit},</if>
            <if test="medianSpecification != null">median_specification = #{medianSpecification},</if>
            <if test="downLimit != null">down_limit = #{downLimit},</if>
            <if test="examine1 != null">examine1 = #{examine1},</if>
            <if test="examine1Ys != null">examine1_ys = #{examine1Ys},</if>
            <if test="examine2 != null">examine2 = #{examine2},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="lastUpdatedBy != null">last_updated_by = #{lastUpdatedBy},</if>
            <if test="creationDate != null">creation_date = #{creationDate},</if>
            <if test="lastUpdateDate != null">last_update_date = #{lastUpdateDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="frequencyUnit != null">frequency_unit = #{frequencyUnit},</if>
            <if test="slotBodyName != null">slot_body_name = #{slotBodyName},</if>
            <if test="projectTeamCode != null">project_team_code = #{projectTeamCode},</if>
            <if test="projectTeamName != null">project_team_name = #{projectTeamName},</if>
            <if test="testCode != null">test_code = #{testCode},</if>
            <if test="adjustmentUpperLimit != null">adjustment_upper_limit = #{adjustmentUpperLimit},</if>
            <if test="adjustmentMid != null">adjustment_mid = #{adjustmentMid},</if>
            <if test="adjustmentLowerLimit != null">adjustment_lower_limit = #{adjustmentLowerLimit},</if>
            <if test="projectUnit != null">project_unit = #{projectUnit},</if>
            <if test="insertionTime != null">insertion_time = #{insertionTime},</if>
            <if test="isExported != null">is_exported = #{isExported},</if>
            <if test="notProcess != null">not_process = #{notProcess},</if>
            <if test="warningUpperLimit != null">warning_upper_limit = #{warningUpperLimit},</if>
            <if test="warningMid != null">warning_mid = #{warningMid},</if>
            <if test="warningLowerLimit != null">warning_lower_limit = #{warningLowerLimit},</if>
            <if test="attribute1 != null">attribute1 = #{attribute1},</if>
            <if test="attribute2 != null">attribute2 = #{attribute2},</if>
            <if test="attribute3 != null">attribute3 = #{attribute3},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteChemicalById" parameterType="String">
        delete from chemical where id = #{id}
    </delete>

    <delete id="deleteChemicalByIds" parameterType="String">
        delete from chemical where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateExportedStatusByIds" parameterType="String">
        update chemical set is_exported = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateProcessedStatusByIds" parameterType="String">
        update chemical set not_process = 0 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="markAsNotProcess" parameterType="String">
        update chemical set not_process = 1 where id = #{id}
    </update>

    <update id="markAsExported" parameterType="String">
        update chemical set is_exported = 1 where id = #{id}
    </update>

    <update id="resetProcessStatus" parameterType="String">
        update chemical set is_exported = 0, not_process = 0 where id = #{id}
    </update>

    <select id="countChemicalData" parameterType="Chemical" resultType="int">
        select count(*) from chemical c
        <where>
            <if test="departmentCode != null  and departmentCode != ''"> and c.department_code = #{departmentCode}</if>
            <if test="processName != null  and processName != ''"> and c.process_name like concat('%', #{processName}, '%')</if>
            <if test="productName != null  and productName != ''"> and c.product_name like concat('%', #{productName}, '%')</if>
            <if test="testName != null  and testName != ''"> and c.test_name like concat('%', #{testName}, '%')</if>
            <if test="isExported != null "> and c.is_exported = #{isExported}</if>
            <if test="notProcess != null "> and c.not_process = #{notProcess}</if>
        </where>
    </select>

    <select id="countUnprocessedData" resultType="int">
        select count(*) from chemical
        where (is_exported = 0 or is_exported is null)
          and (not_process = 0 or not_process is null)
          and upper_limit is not null
          and upper_limit != ''
          and upper_limit != 'null'
          and examine_date is not null
    </select>

    <select id="countExportedData" resultType="int">
        select count(*) from chemical where is_exported = 1
    </select>

    <select id="countTodayNewData" resultType="int">
        select count(*) from chemical
        where date_format(create_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

</mapper>
