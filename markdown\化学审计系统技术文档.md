# 化学审计系统技术文档

## 1. 项目概述

### 1.1 项目背景
本项目旨在将原有的JavaFX桌面应用程序（chemical和chemical-refresh）迁移到若依框架下，实现Web化的化学数据审计系统。系统主要功能包括：
- 从Pulsar消息队列接收化学检测数据
- 对数据进行处理和调整
- 提供数据查询、导出、监控等功能
- 支持数据刷新和规则管理

### 1.2 技术架构
- **后端框架**: 若依框架 (Spring Boot + MyBatis)
- **前端框架**: Vue.js + Element UI
- **数据库**: SQL Server
- **消息队列**: Apache Pulsar
- **文件导出**: Apache POI (Excel), CSV

### 1.3 系统模块
- **数据监控模块**: 实时监控任务状态和数据处理进度
- **任务控制模块**: 控制数据读取、处理任务的启动、暂停、停止
- **数据管理模块**: 管理原始数据、应审数据、数据日志
- **规则管理模块**: 管理数据处理规则
- **数据导出模块**: 支持CSV和Excel格式导出
- **系统配置模块**: 管理系统参数配置

## 2. 数据库设计

### 2.1 核心表结构

#### 2.1.1 chemical (化学检测数据主表)
存储从Pulsar消息队列接收的原始化学检测数据。

```sql
CREATE TABLE chemical (
    id VARCHAR(50) PRIMARY KEY,                    -- 主键ID
    organization_id BIGINT,                        -- 组织ID
    attribute_id BIGINT,                           -- 属性ID
    examine_date DATETIME,                         -- 检测日期
    shift VARCHAR(20),                             -- 班次
    staff VARCHAR(100),                            -- 操作员工
    department_code VARCHAR(50),                   -- 部门代码
    process_name VARCHAR(100),                     -- 工艺名称
    product_name VARCHAR(100),                     -- 产品名称
    test_name VARCHAR(100),                        -- 测试名称
    layer_number VARCHAR(50),                      -- 层号
    upper_limit VARCHAR(20),                       -- 上限
    median_specification VARCHAR(20),              -- 中位规格
    down_limit VARCHAR(20),                        -- 下限
    examine1 VARCHAR(20),                          -- 检测值1
    examine2 VARCHAR(20),                          -- 检测值2
    is_exported BIT DEFAULT 0,                     -- 是否已导出
    not_process BIT DEFAULT 0,                     -- 是否不处理
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
    -- 其他字段...
);
```

#### 2.1.2 chemical_ys (化学应审数据表)
存储经过处理和调整后的化学检测数据。

```sql
CREATE TABLE chemical_ys (
    id VARCHAR(50) PRIMARY KEY,                    -- 主键ID (关联chemical表)
    examine1 VARCHAR(20),                          -- 调整后检测值1
    examine2 VARCHAR(20),                          -- 调整后检测值2
    examine1_zs VARCHAR(20),                       -- 检测值1真实值
    is_modified BIT DEFAULT 0,                     -- 是否被修改过
    original_examine1 VARCHAR(20),                 -- 原始检测值1
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
    -- 其他字段与chemical表类似...
);
```

#### 2.1.3 chemical_rule (处理规则表)
存储特殊的数据处理规则。

```sql
CREATE TABLE chemical_rule (
    rule_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 规则ID
    product_name VARCHAR(100) NOT NULL,            -- 产品名称
    process_name VARCHAR(100) NOT NULL,            -- 工艺名称
    test_name VARCHAR(100) NOT NULL,               -- 测试名称
    is_refresh BIT DEFAULT 0,                      -- 是否刷新
    rule_type VARCHAR(20) DEFAULT 'NORMAL',        -- 规则类型
    rule_desc VARCHAR(200),                        -- 规则描述
    rule_config TEXT,                              -- 规则配置(JSON格式)
    status CHAR(1) DEFAULT '0',                    -- 状态
    create_time DATETIME DEFAULT GETDATE()         -- 创建时间
);
```

#### 2.1.4 chemical_task_monitor (任务监控表)
记录任务执行状态和监控信息。

```sql
CREATE TABLE chemical_task_monitor (
    task_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 任务ID
    task_name VARCHAR(100) NOT NULL,               -- 任务名称
    task_type VARCHAR(20) NOT NULL,                -- 任务类型
    task_status VARCHAR(20) DEFAULT 'WAITING',     -- 任务状态
    start_time DATETIME,                           -- 开始时间
    end_time DATETIME,                             -- 结束时间
    total_records INT DEFAULT 0,                   -- 总记录数
    processed_records INT DEFAULT 0,               -- 已处理记录数
    success_records INT DEFAULT 0,                 -- 成功记录数
    error_records INT DEFAULT 0,                   -- 错误记录数
    progress_percent DECIMAL(5,2) DEFAULT 0,       -- 进度百分比
    create_time DATETIME DEFAULT GETDATE()         -- 创建时间
);
```

### 2.2 数据库初始化
执行以下SQL文件进行数据库初始化：
1. `sql/chemical_audit_tables.sql` - 创建表结构
2. `sql/chemical_audit_menu.sql` - 创建菜单权限

## 3. 后端架构设计

### 3.1 包结构
```
com.ruoyi.system
├── domain/                 # 实体类
│   ├── Chemical.java
│   ├── ChemicalYs.java
│   ├── ChemicalRule.java
│   └── ChemicalTaskMonitor.java
├── mapper/                 # 数据访问层
│   ├── ChemicalMapper.java
│   ├── ChemicalYsMapper.java
│   ├── ChemicalRuleMapper.java
│   └── ChemicalTaskMonitorMapper.java
├── service/                # 服务接口
│   ├── IChemicalService.java
│   ├── IChemicalTaskService.java
│   └── IChemicalExportService.java
└── service/impl/           # 服务实现
    ├── ChemicalServiceImpl.java
    ├── ChemicalTaskServiceImpl.java
    └── ChemicalExportServiceImpl.java
```

### 3.2 核心服务类

#### 3.2.1 IChemicalService
化学数据管理服务，提供基础的CRUD操作和数据处理功能。

主要方法：
- `selectChemicalList()` - 查询化学数据列表
- `insertChemical()` - 新增化学数据
- `processChemicalData()` - 处理化学数据
- `processMessageData()` - 处理Pulsar消息数据

#### 3.2.2 IChemicalTaskService
任务控制服务，管理数据读取、处理任务的生命周期。

主要方法：
- `startDataReadingTask()` - 启动数据读取任务
- `pauseDataReadingTask()` - 暂停数据读取任务
- `stopDataReadingTask()` - 停止数据读取任务
- `getTaskStatus()` - 获取任务状态
- `getTaskMonitorInfo()` - 获取任务监控信息

#### 3.2.3 IChemicalExportService
数据导出服务，支持CSV和Excel格式导出。

主要方法：
- `exportToCSV()` - 导出CSV文件
- `exportToExcel()` - 导出Excel文件
- `getExportHistory()` - 获取导出历史记录
- `downloadExportFile()` - 下载导出文件

### 3.3 数据处理流程

#### 3.3.1 Pulsar消息处理流程
1. 初始化Pulsar客户端和消费者
2. 监听指定主题的消息
3. 接收到消息后解析为Chemical对象
4. 验证数据完整性
5. 插入到chemical表
6. 记录处理日志

#### 3.3.2 数据处理流程
1. 查询未处理的chemical数据
2. 连接远程SPC-G2数据库获取控制限制参数
3. 根据规则计算调整后的检测值
4. 插入处理后的数据到chemical_ys表
5. 更新原数据的处理状态

## 4. 前端架构设计

### 4.1 页面结构
```
src/views/chemical/
├── monitor/                # 数据监控
│   └── index.vue
├── task/                   # 任务控制
│   └── index.vue
├── data/                   # 数据管理
│   ├── chemical/
│   │   └── index.vue
│   ├── chemical-ys/
│   │   └── index.vue
│   └── log/
│       └── index.vue
├── rule/                   # 规则管理
│   └── index.vue
├── export/                 # 数据导出
│   └── index.vue
└── config/                 # 系统配置
    └── index.vue
```

### 4.2 核心组件

#### 4.2.1 数据监控页面 (monitor/index.vue)
- 实时显示任务状态
- 显示处理进度和统计信息
- 实时日志输出
- 系统状态监控

#### 4.2.2 任务控制页面 (task/index.vue)
- 任务启动、暂停、停止控制
- 任务配置管理
- Pulsar连接状态监控
- 任务历史记录

#### 4.2.3 数据管理页面
- 原始数据查询和管理
- 应审数据查询和编辑
- 数据重新处理功能
- 数据状态管理

## 5. 核心功能实现

### 5.1 Pulsar消息队列集成
```java
// Pulsar客户端配置
PulsarClient pulsarClient = PulsarClient.builder()
    .serviceUrl("pulsar://pulsar.scc.com:6650")
    .authentication(AuthenticationFactory.token(TOKEN))
    .build();

// 消费者配置
Consumer<byte[]> consumer = pulsarClient.newConsumer(Schema.BYTES)
    .subscriptionName("G2-Chemical-Web")
    .subscriptionType(SubscriptionType.Exclusive)
    .topic("persistent://spc/331-interface/labMedicineData")
    .subscribe();
```

### 5.2 数据处理算法
```java
// 控制值计算
private CalculatedValues calculateControlValues(ControlLimits controlLimits, String testName) {
    double intermediateValue = 6 * controlLimits.fSp;
    
    // 特殊测试项目的样本量调整
    Map<String, Integer> sampleSizeMap = new HashMap<>();
    sampleSizeMap.put("PD全线微蚀量", 3);
    sampleSizeMap.put("TR微蚀微蚀量", 5);
    
    if (sampleSizeMap.containsKey(testName)) {
        int sampleSize = sampleSizeMap.get(testName);
        intermediateValue = intermediateValue / Math.sqrt(sampleSize);
    }
    
    double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
    double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
    double lcl1 = (7.0 / 8.0) * lowerControlLimit + (1.0 / 8.0) * upperControlLimit;
    double ucl1 = (1.0 / 8.0) * lowerControlLimit + (7.0 / 8.0) * upperControlLimit;
    
    return new CalculatedValues(upperControlLimit, lowerControlLimit, lcl1, ucl1);
}
```

### 5.3 数据导出功能
支持CSV和Excel两种格式的数据导出：
- 按时间范围筛选数据
- 按层号筛选数据
- 自动生成文件名
- 支持文件下载和历史记录管理

## 6. 系统配置

### 6.1 必需的系统配置
```properties
# Pulsar配置
pulsar.broker.url=pulsar://pulsar.scc.com:6650
pulsar.auth.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzMxIn0.U7QrwJTShmbcucMtIqqzC5gfK8mbgsSAF20yyed1W6A
pulsar.topic.medicine=persistent://spc/331-interface/labMedicineData
pulsar.subscription.name=G2-Chemical-Web

# 数据库配置
database.local.url=*****************************************************************
database.cloud.url=************************************************************************************************************

# 导出配置
export.file.path=E:\\测试数据\\应审抛转数据\\G2
export.backup.path=

# 任务配置
task.schedule.interval=30
data.process.batch.size=100
```

### 6.2 菜单权限配置
系统提供完整的菜单权限体系：
- 一级菜单：化学审计
- 二级菜单：数据监控、任务控制、数据管理、规则管理、数据导出、系统配置
- 按钮权限：查询、新增、修改、删除、导出、启动任务、暂停任务等

## 7. 部署说明

### 7.1 环境要求
- JDK 1.8+
- SQL Server 2012+
- Apache Pulsar
- Node.js 12+ (前端构建)

### 7.2 部署步骤
1. 执行数据库初始化脚本
2. 配置application.yml中的数据库连接
3. 配置Pulsar连接参数
4. 构建前端项目
5. 启动后端服务
6. 访问系统并配置权限

### 7.3 注意事项
- 确保Pulsar服务正常运行
- 确保远程SPC-G2数据库连接正常
- 配置正确的文件导出路径
- 定期清理历史任务记录和导出文件

## 8. 后续开发计划

### 8.1 待完成功能
- 前端页面开发
- 控制器层实现
- 完整的数据处理逻辑
- 实时WebSocket通信
- 系统监控和告警

### 8.2 优化方向
- 性能优化
- 错误处理完善
- 日志记录优化
- 安全性增强
- 用户体验改进

---

*本文档将随着项目进展持续更新*
