package com.ruoyi.system.domain;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 任务监控对象 chemical_task_monitor
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ChemicalTaskMonitor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务类型 */
    @Excel(name = "任务类型", dictType = "chemical_task_type")
    private String taskType;

    /** 任务状态 */
    @Excel(name = "任务状态", dictType = "chemical_task_status")
    private String taskStatus;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 总记录数 */
    @Excel(name = "总记录数")
    private Integer totalRecords;

    /** 已处理记录数 */
    @Excel(name = "已处理记录数")
    private Integer processedRecords;

    /** 成功记录数 */
    @Excel(name = "成功记录数")
    private Integer successRecords;

    /** 错误记录数 */
    @Excel(name = "错误记录数")
    private Integer errorRecords;

    /** 进度百分比 */
    @Excel(name = "进度百分比")
    private BigDecimal progressPercent;

    /** 错误信息 */
    private String errorMessage;

    /** 任务配置 */
    private String taskConfig;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }

    public void setTaskType(String taskType) 
    {
        this.taskType = taskType;
    }

    public String getTaskType() 
    {
        return taskType;
    }

    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() 
    {
        return taskStatus;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setTotalRecords(Integer totalRecords) 
    {
        this.totalRecords = totalRecords;
    }

    public Integer getTotalRecords() 
    {
        return totalRecords;
    }

    public void setProcessedRecords(Integer processedRecords) 
    {
        this.processedRecords = processedRecords;
    }

    public Integer getProcessedRecords() 
    {
        return processedRecords;
    }

    public void setSuccessRecords(Integer successRecords) 
    {
        this.successRecords = successRecords;
    }

    public Integer getSuccessRecords() 
    {
        return successRecords;
    }

    public void setErrorRecords(Integer errorRecords) 
    {
        this.errorRecords = errorRecords;
    }

    public Integer getErrorRecords() 
    {
        return errorRecords;
    }

    public void setProgressPercent(BigDecimal progressPercent) 
    {
        this.progressPercent = progressPercent;
    }

    public BigDecimal getProgressPercent() 
    {
        return progressPercent;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public void setTaskConfig(String taskConfig) 
    {
        this.taskConfig = taskConfig;
    }

    public String getTaskConfig() 
    {
        return taskConfig;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("taskType", getTaskType())
            .append("taskStatus", getTaskStatus())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("totalRecords", getTotalRecords())
            .append("processedRecords", getProcessedRecords())
            .append("successRecords", getSuccessRecords())
            .append("errorRecords", getErrorRecords())
            .append("progressPercent", getProgressPercent())
            .append("errorMessage", getErrorMessage())
            .append("taskConfig", getTaskConfig())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
