package org.example;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


public class Main {
    public static String saveFilePath="E:\\测试数据\\应审抛转数据\\G2";
    public static String backupFilePath;
    public static boolean isDataExport = false;
    public static boolean isRefresh = false;
    private static Random random = new Random();
    private static LocalDateTime startDateTime;
    private static LocalDateTime endDateTime;
    private static final HikariDataSource dataSource;
    private static final HikariDataSource dataSourceCloud;

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*****************************************************************");
        config.setUsername("sa");
        config.setPassword("root1234");
        config.setMaximumPoolSize(50); // 根据需要调整池大小
        config.setMinimumIdle(5);
        config.setIdleTimeout(30000);
        config.setMaxLifetime(60000);
        dataSource = new HikariDataSource(config);
    }

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:sqlserver://************;DatabaseName=SPC-G2;encrypt=true;trustServerCertificate=true;" +
                "sslProtocol=TLSv1");
        config.setUsername("hhh");
        config.setPassword("root1234");
        config.setMaximumPoolSize(50); // 根据需要调整池大小
        config.setMinimumIdle(5);
        config.setIdleTimeout(30000);
        config.setMaxLifetime(60000);
        config.setPoolName("CloudDataSourcePool"); // 设置数据源的名称
        config.setRegisterMbeans(true); // 启用JMX监控
        dataSourceCloud = new HikariDataSource(config);
    }

    /**
     * 主方法，程序的入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 定期检查新数据
        runMainLogic();
    }

    /**
     * 设置处理日期范围
     *
     * @param start 开始日期
     * @param end   结束日期
     */
    public static void setProcessingDates(LocalDateTime start, LocalDateTime end) {
        startDateTime = start;
        endDateTime   = end;
    }



    // 修改 insertDataIntoDatabase 方法以插入数据


    private static String getF_PRCSValueForDepartment(Connection remoteConnection, String departmentName) {
        String sql = "SELECT F_PRCS FROM dbo.PRCS_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, departmentName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_PRCS");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PRCS 值
    }

    private static String getF_TESTValueForTestName(Connection remoteConnection, String testName) {
        String sql = "SELECT F_TEST FROM dbo.TEST_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, testName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_TEST");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_TEST 值
    }

    private static String getF_PARTValueForProductName(Connection remoteConnection, String productName) {
        String sql = "SELECT F_PART FROM dbo.PART_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, productName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
//                    System.out.println(productName);
                    return resultSet.getString("F_PART");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PART 值
    }

    public static void runMainLogic() {
        Connection localConnection = null;
        Connection remoteConnection = null;
        long startTime = System.currentTimeMillis();

        try {
            localConnection = dataSource.getConnection();
            localConnection.setAutoCommit(false);
            remoteConnection = dataSourceCloud.getConnection();

            if (isRefresh) {
                System.out.println("=== 开始数据刷新 ===");
                List<List<Chemical>> processedGroups = processTestData(dataSource.getConnection(), dataSourceCloud.getConnection());
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                // 统计刷新的项目数量
                int totalProjects = processedGroups.size();
                int modifiedRecords = processedGroups.stream()
                    .mapToInt(group -> (int) group.stream().filter(Chemical::isExamine1Modified).count())
                    .sum();

                System.out.println("=== 数据刷新完成 ===");
                System.out.println("处理项目组数: " + totalProjects);
                System.out.println("修改记录数: " + modifiedRecords);
                System.out.println("总耗时: " + duration + " 毫秒 (" + String.format("%.2f", duration / 1000.0) + " 秒)");

            } else if (isDataExport) {
                System.out.println("=== 开始数据导出 ===");
                System.out.println("导出时间范围: " + startDateTime + " 至 " + endDateTime);

                // 同时导出CSV和Excel
                processYsChemicalData(localConnection, saveFilePath, backupFilePath, isDataExport, false);
                exportDataToExcel(localConnection, saveFilePath, startDateTime, endDateTime);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                System.out.println("=== 数据导出完成 ===");
                System.out.println("总耗时: " + duration + " 毫秒 (" + String.format("%.2f", duration / 1000.0) + " 秒)");
            }

        } catch (IOException e) {
            System.err.println("IO异常: " + e.getMessage());
            throw new RuntimeException(e);
        } catch (SQLException e) {
            System.err.println("数据库异常: " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            // 确保连接最终被关闭
            try {
                if (localConnection != null) {
                    localConnection.close();
                }
                if (remoteConnection != null) {
                    remoteConnection.close();
                }
            } catch (SQLException e) {
                System.err.println("关闭连接时发生异常: " + e.getMessage());
            }
        }
    }

    static class ControlLimitsCache {
        private static final Map<String, ControlLimits> cache = new ConcurrentHashMap<>();

        public static ControlLimits getControlLimits(Connection remoteConnection, String fPrcs, String fTest,
                                                     String fPart) {
            // 构建一个唯一的键来标识每个不同的参数组合
            String key = fPrcs + "_" + fTest + "_" + fPart;

            // 首先尝试从缓存中获取
            if (cache.containsKey(key)) {
                return cache.get(key);
            }

//             如果缓存中没有，则从数据库查询并更新缓存
            if (fPrcs != null && fTest != null && fPart != null) {
                String sql = "SELECT F_MEAN, F_SP FROM dbo.CTRL_LIM WHERE F_PRCS = ? AND F_TEST = ? AND F_PART = ? ORDER BY F_CTRL DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
                try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
                    preparedStatement.setString(1, fPrcs);
                    preparedStatement.setString(2, fTest);
                    preparedStatement.setString(3, fPart);
                    try (ResultSet resultSet = preparedStatement.executeQuery()) {
                        if (resultSet.next()) {
                            double fMean = resultSet.getDouble("F_MEAN");
                            double fSp = resultSet.getDouble("F_SP");
                            ControlLimits limits = new ControlLimits(fMean, fSp);
                            cache.put(key, limits); // 更新缓存
                            return limits;
                        }
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }
    }

    public static void processYsChemicalData(Connection localConnection, String csvFilePath, String backupFilePath, boolean isDataExport, boolean isTimeTask) throws SQLException, IOException {
        String sql = "SELECT * FROM dbo.chemical_ys WHERE (layer_number = '常规分析' OR layer_number = '输入错误' OR layer_number = '开缸后分析' OR layer_number = '保养后分析') ";

        if (isDataExport) {
            // 导出选定日期范围内的数据
            sql += "AND examine_date >= ? AND examine_date <= ?";
        }

        System.out.println("执行SQL查询: " + sql);
        System.out.println("查询时间范围: " + startDateTime + " 至 " + endDateTime);

        try (PreparedStatement stmt = localConnection.prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY)) {
            if (isDataExport) {
                stmt.setTimestamp(1, Timestamp.valueOf(startDateTime));
                stmt.setTimestamp(2, Timestamp.valueOf(endDateTime));
            }

            try (ResultSet resultSet = stmt.executeQuery()) {
                exportToCSV(resultSet, csvFilePath, backupFilePath, localConnection);
                System.out.println("CSV数据导出完成");
            }
        } catch (SQLException e) {
            System.err.println("SQL执行出错：" + e.getMessage());
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            System.err.println("发生异常：" + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }



    public static void exportToCSV(ResultSet resultSet, String csvFilePath, String backupFilePath, Connection localConnection) throws SQLException, IOException {
        LocalDateTime minExamineDate = null, maxExamineDate = null;
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        int recordCount = 0;

        // 统计记录数量并找到时间范围
        while (resultSet.next()) {
            recordCount++;
            LocalDateTime examineDate = resultSet.getTimestamp("examine_date").toLocalDateTime();
            if (minExamineDate == null || examineDate.isBefore(minExamineDate)) {
                minExamineDate = examineDate;
            }
            if (maxExamineDate == null || examineDate.isAfter(maxExamineDate)) {
                maxExamineDate = examineDate;
            }
        }

        if (minExamineDate != null && maxExamineDate != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
            String currentDateTime = dateFormat.format(new Date());
            String formattedFilePath = csvFilePath + "\\" + minExamineDate.format(dateFormatter) + "_to_" + maxExamineDate.format(dateFormatter) + "_outTime_" + currentDateTime + "_chemicalOutput.csv";

            System.out.println("导出记录数: " + recordCount);
            System.out.println("数据时间范围: " + minExamineDate.format(dateTimeFormatter) + " 至 " + maxExamineDate.format(dateTimeFormatter));
            System.out.println("CSV文件路径: " + formattedFilePath);

            writeCsvData(resultSet, formattedFilePath, dateTimeFormatter, localConnection);

            if (backupFilePath != null && !backupFilePath.isEmpty()) {
                String backupFormattedFilePath = backupFilePath + "\\" + minExamineDate.format(dateFormatter) + "_to_" + maxExamineDate.format(dateFormatter) + "_outTime_" + currentDateTime + "_chemicalOutput.csv";
                System.out.println("备份文件路径: " + backupFormattedFilePath);
                writeCsvData(resultSet, backupFormattedFilePath, dateTimeFormatter, localConnection);
            }
        } else {
            System.out.println("未找到符合条件的数据记录");
        }
    }

    private static void writeCsvData(ResultSet resultSet, String filePath, DateTimeFormatter dateTimeFormatter, Connection localConnection) throws SQLException, IOException {
        try (PrintWriter writer = new PrintWriter(new OutputStreamWriter(new FileOutputStream(filePath, true), "GBK"))) {
            writer.println("id,organization_id,attribute_id,examine_date,shift,staff,process_set_name,process_name,product_set_name,product_name,test_set_name,test_name,sample_size,layer_number,upper_limit,median_specification,down_limit,examine1,examine2,created_by,last_updated_by,creation_date,last_update_date,status,frequency,frequency_unit,slot_body_name,project_team_code,project_team_name,test_code,adjustment_upper_limit,adjustment_mid,adjustment_lower_limit,project_unit,warning_upper_limit,warning_mid,warning_lower_limit");
            resultSet.beforeFirst(); // 重置resultSet的游标，以便重新遍历数据
            while (resultSet.next()) {
                if ("null".equals(resultSet.getString("upper_limit"))) {
                    continue;
                }
                StringBuilder csvLine = new StringBuilder();
                csvLine.append(resultSet.getString("id")).append(",")
                        .append(resultSet.getString("organization_id")).append(",")
                        .append(resultSet.getString("attribute_id")).append(",")
                        .append(resultSet.getTimestamp("examine_date").toLocalDateTime().format(dateTimeFormatter)).append(",")
                        .append(resultSet.getString("shift")).append(",")
                        .append(resultSet.getString("staff")).append(",")
                        .append(resultSet.getString("process_set_name")).append(",")
                        .append(resultSet.getString("process_name")).append(",")
                        .append(resultSet.getString("product_set_name")).append(",")
                        .append(resultSet.getString("product_name")).append(",")
                        .append(resultSet.getString("test_set_name")).append(",")
                        .append(resultSet.getString("test_name")).append(",")
                        .append(resultSet.getString("sample_size")).append(",")
                        .append(resultSet.getString("layer_number")).append(",")
                        .append(resultSet.getString("upper_limit")).append(",")
                        .append(resultSet.getString("median_specification")).append(",")
                        .append(resultSet.getString("down_limit")).append(",")
                        .append(resultSet.getString("examine1")).append(",")
                        .append(resultSet.getString("examine2")).append(",")
                        .append(resultSet.getString("created_by")).append(",")
                        .append(resultSet.getString("last_updated_by")).append(",")
                        .append(resultSet.getTimestamp("creation_date").toLocalDateTime().format(dateTimeFormatter)).append(",")
                        .append(resultSet.getTimestamp("last_update_date").toLocalDateTime().format(dateTimeFormatter)).append(",")
                        .append(resultSet.getString("status")).append(",")
                        .append(resultSet.getString("frequency")).append(",")
                        .append(resultSet.getString("frequency_unit")).append(",")
                        .append(resultSet.getString("slot_body_name")).append(",")
                        .append(resultSet.getString("project_team_code")).append(", ")
                        .append(resultSet.getString("project_team_name")).append(",")
                        .append(resultSet.getString("test_code")).append(",")
                        .append(resultSet.getString("adjustment_upper_limit")).append(",")
                        .append(resultSet.getString("adjustment_mid")).append(",")
                        .append(resultSet.getString("adjustment_lower_limit")).append(",")
                        .append(resultSet.getString("project_unit")).append(",")
                        .append(resultSet.getString("warning_upper_limit")).append(",")
                        .append(resultSet.getString("warning_mid")).append(",")
                        .append(resultSet.getString("warning_lower_limit"));
                writer.println(csvLine);
            }
        }
    }

    public static void exportDataToExcel(Connection localConnection, String excelFilePath, LocalDateTime startDate, LocalDateTime endDate) throws SQLException, IOException {
        System.out.println("开始导出Excel文件...");

        // 获取当前日期时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String currentDateTime = dateFormat.format(new Date());

        // 构建新的文件路径
        excelFilePath = excelFilePath + "\\" + currentDateTime + "_chemicalOutput.xlsx";
        System.out.println("Excel文件路径: " + excelFilePath);

        String localSql = "SELECT shift, process_name, product_name, slot_body_name, project_team_name, project_unit, frequency, frequency_unit,upper_limit, median_specification, down_limit, examine1, examine2,  examine_date FROM dbo.chemical_ys dc " + (startDate != null && endDate != null ? "WHERE examine_date >= ? AND examine_date <= ? ORDER BY process_name, examine_date ASC" : "ORDER BY process_name, examine_date ASC");

        Workbook workbook = new XSSFWorkbook(); // 创建Excel工作簿

        // 创建CellStyle用于表头
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true); // 加粗字体
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        headerStyle.setFont(headerFont);
        // 创建一个颜色对象
        byte[] rgb = new byte[3];
        rgb[0] = (byte) 172; // 红色
        rgb[1] = (byte) 185; // 绿色
        rgb[2] = (byte) 202; // 蓝色
        XSSFColor myColor = new XSSFColor(rgb, null); // 新的XSSFColor构造方式

        headerStyle.setFillForegroundColor(myColor);
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Map<String, Sheet> sheetsMap = new LinkedHashMap<>(); // 用于存储不同processName的Sheet

        try (PreparedStatement localPreparedStatement = localConnection.prepareStatement(localSql)) {
            if (startDate != null && endDate != null) {
                localPreparedStatement.setTimestamp(1, Timestamp.valueOf(startDateTime));
                localPreparedStatement.setTimestamp(2, Timestamp.valueOf(endDateTime));
            }

            ResultSet resultSet = localPreparedStatement.executeQuery();
            while (resultSet.next()) {
                String processName = resultSet.getString("process_name");
                Sheet sheet = sheetsMap.computeIfAbsent(processName, k -> workbook.createSheet(processName)); // 根据processName获取或创建Sheet

                int rownum = sheet.getLastRowNum();
                Row row = sheet.createRow(rownum + 1);

                // 确定要导出的列和中文表头
                String[] headers = {"班次", "过程", "产品", "槽体名称", "项目组名称", "项目单位", "分析频次", "频次单位", "规格上限", "规格中值", "规格下限", "分析值", "测量值", "分析时间"};
                String[] columnNames = {"shift", "process_name", "product_name", "slot_body_name", "project_team_name", "project_unit", "frequency", "frequency_unit", "upper_limit", "median_specification", "down_limit", "examine1", "examine2", "examine_date"};

                if (rownum == 0) { // 如果是新Sheet，添加表头
                    Row headerRow = sheet.createRow(0);
                    for (int i = 0; i < headers.length; i++) {
                        Cell cell = headerRow.createCell(i);
                        cell.setCellValue(headers[i]);
                        cell.setCellStyle(headerStyle); // 应用表头样式
                    }
                }

                CellStyle numberStyle = workbook.createCellStyle();
                numberStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00")); // 数字格式

                for (int i = 0; i < columnNames.length; i++) {
                    Cell cell = row.createCell(i);
                    String value = resultSet.getString(columnNames[i]);
                    String valueName = "null";
                    // 对特定列内容设置为数字格式
                    if (i >= 8 && i <= 12) { // 对规格上限、规格中值、规格下限、分析值、测量值列设置数字格式
                        cell.setCellStyle(numberStyle);
                        cell.setCellValue(!Objects.equals(value, valueName) ? Double.parseDouble(value) : 0);
                    } else {
                        cell.setCellValue(value == null ? "" : value);
                    }
                }
            }
        }

        // 自适应列宽需要评估最长的单元格内容
        DataFormatter formatter = new DataFormatter(); // 用于格式化内容为字符串
        // 对每个Sheet进行自适应列宽设置
        sheetsMap.values().forEach(sheet -> {
            for (int columnIndex = 0; columnIndex < 14; columnIndex++) {
                int maxColumnWidth = 0;
                for (Row row : sheet) {
                    Cell cell = row.getCell(columnIndex);
                    int cellWidth = workbook.getFontAt(cell.getCellStyle().getFontIndex()).getFontHeightInPoints() * formatter.formatCellValue(cell).length();
                    if (cellWidth > maxColumnWidth) {
                        maxColumnWidth = cellWidth;
                    }
                }
                sheet.setColumnWidth(columnIndex, (int) (maxColumnWidth * 48)); // 设置列宽，可能需要根据实际情况调整放大系数
            }
        });

        try (FileOutputStream outputStream = new FileOutputStream(excelFilePath)) {
            workbook.write(outputStream); // 将工作簿写入文件
            workbook.close();
            System.out.println("Excel文件导出完成");
        }
    }

    public static List<List<Chemical>> processTestData(Connection localConnection, Connection remoteConnection) throws SQLException {
        long methodStartTime = System.currentTimeMillis();
        System.out.println("开始查询数据库...");

        String sql = "SELECT id, organization_id, attribute_id, examine_date, shift, staff," +
                "process_name, product_set_name, product_name, test_set_name, test_name, sample_size, " +
                "layer_number, upper_limit, median_specification, down_limit, examine2, created_by, " +
                "last_updated_by, creation_date, last_update_date, status, frequency, frequency_unit," +
                "slot_body_name, project_team_code, project_team_name, test_code, adjustment_upper_limit," +
                "adjustment_mid, adjustment_lower_limit, project_unit, examine1, warning_upper_limit, warning_mid," +
                "warning_lower_limit" +
                " FROM ( SELECT id, organization_id, attribute_id, examine_date, shift, staff," +
                "process_name, product_set_name, product_name, test_set_name, test_name, sample_size, " +
                "layer_number, upper_limit, median_specification, down_limit, examine2, created_by, " +
                "last_updated_by, creation_date, last_update_date, status, frequency, frequency_unit," +
                "slot_body_name, project_team_code, project_team_name, test_code, adjustment_upper_limit," +
                "adjustment_mid, adjustment_lower_limit, project_unit, examine1, warning_upper_limit, warning_mid," +
                "warning_lower_limit, ROW_NUMBER() OVER (PARTITION BY process_name, product_name, test_name ORDER BY examine_date DESC) AS rn " +
                "FROM dbo.chemical_ys " +
                "WHERE layer_number" +
                " IN ('常规分析', '输入错误','开缸后分析','保养后分析')) AS sub " +
                "WHERE rn <= 50" +
                " ORDER BY process_name, product_name, test_name, examine_date ";

        // 使用ConcurrentHashMap进行并发处理
        Map<String, List<Chemical>> testGroupsMap = new ConcurrentHashMap<>();

        // 缓存常用查询结果以避免重复查询
        Map<String, Rule> ruleCache = new ConcurrentHashMap<>();
        Map<String, String> fTestValueCache = new ConcurrentHashMap<>();
        Map<String, String> fPrcsValueCache = new ConcurrentHashMap<>();
        Map<String, String> fPartValueCache = new ConcurrentHashMap<>();

        try (PreparedStatement preparedStatement = localConnection.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY)) {
            preparedStatement.setFetchSize(100); // 增加fetchSize，提高查询性能
            ResultSet resultSet = preparedStatement.executeQuery();

            int recordCount = 0;
            int validRecordCount = 0;

            while (resultSet.next()) {
                recordCount++;
                if ("null".equals(resultSet.getString("upper_limit"))||"null".equals(resultSet.getString("down_limit"))) {
                    continue;
                }
                validRecordCount++;

                // 构建每个记录的唯一标识符，例如"processName_productName_testName"
                String groupKey = resultSet.getString("process_name") + "~" +
                        resultSet.getString("product_name") + "~" +
                        resultSet.getString("test_name");
                Chemical chemical = mapRowToChemical(resultSet); // 封装数据映射逻辑

                // 使用CopyOnWriteArrayList保证并发安全
                testGroupsMap.computeIfAbsent(groupKey, k -> new CopyOnWriteArrayList<>()).add(chemical);
            }

            long queryEndTime = System.currentTimeMillis();
            System.out.println("数据库查询完成，总记录数: " + recordCount + ", 有效记录数: " + validRecordCount +
                             ", 项目组数: " + testGroupsMap.size() + ", 查询耗时: " + (queryEndTime - methodStartTime) + "ms");

            System.out.println("开始数据处理...");
            long processingStartTime = System.currentTimeMillis();

            List<List<Chemical>> processedTestGroups = testGroupsMap.entrySet().parallelStream().map(entry -> {
                List<Chemical> testGroup = entry.getValue();
                String[] parts = entry.getKey().split("~");
                String processName = parts[0];
                String productName = parts[1];
                String testName = parts[2];

                System.out.println("处理项目: " + processName + " - " + productName + " - " + testName + " (记录数: " + testGroup.size() + ")");

                // 检查特定条件
                if ("基材减铜_01".equals(productName) && "钻孔".equals(processName) && "FE纯水Cl-".equals(testName)) {
                    System.out.println("  应用特殊规则: 设置为0");
                    for (Chemical chemical : testGroup) {
                        chemical.setExamine1("0");
                        chemical.setExamine2("0");
                    }
                } else {
                    Rule rule = ruleCache.computeIfAbsent(productName + "~" + processName + "~" + testName,
                            key -> getRuleForTest(localConnection, productName, processName, testName));

                    String fTestValue = fTestValueCache.computeIfAbsent(testName, key -> getF_TESTValueForTestName(remoteConnection, key));
                    String fPrcsValue = fPrcsValueCache.computeIfAbsent(processName, key -> getF_PRCSValueForDepartment(remoteConnection, key));
                    String fPartValue = fPartValueCache.computeIfAbsent(productName, key -> getF_PARTValueForProductName(remoteConnection, key));

                    if (fTestValue != null && fPrcsValue != null && fPartValue != null) {
                        ControlLimits controlLimits = ControlLimitsCache.getControlLimits(remoteConnection, fPrcsValue, fTestValue, fPartValue);
                        if (controlLimits != null) {
                            System.out.println("  获取到控制限: Mean=" + String.format("%.3f", controlLimits.fMean) +
                                             ", Sp=" + String.format("%.3f", controlLimits.fSp));
                            double intermediateValue = 3 * controlLimits.fSp;
                            double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
                            double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
                            boolean conditionsMet = false;
                            int attempt = 0;
                            while (!conditionsMet && attempt < 5) {
                                if (rule != null) {
                                    System.out.println("  应用规则: " + (rule.isRefresh ? "刷新模式" : "CPK模式"));
                                    if (rule.isRefresh) {
                                        adjustForControlLimits(testGroup, controlLimits.fMean, upperControlLimit, lowerControlLimit);
                                        applySPCRules(testGroup, controlLimits);
                                        conditionsMet = true; // 刷新模式只执行一次
                                        attempt++;
                                    } else {
                                        adjustDataForCPKWithNewMethod(testGroup, rule);
                                        double cpk = calculateCPK(testGroup, rule);
                                        conditionsMet = cpk >= 1.33;
                                        System.out.println("  CPK值: " + String.format("%.3f", cpk) + (conditionsMet ? " (满足条件)" : " (需要调整)"));
                                        attempt++;
                                    }
                                } else {
                                    System.out.println("  应用默认处理规则");
                                    adjustForControlLimits(testGroup, controlLimits.fMean, upperControlLimit, lowerControlLimit);
                                    applySPCRules(testGroup, controlLimits);
                                    adjustDataForCPK(testGroup, controlLimits.fMean, upperControlLimit, lowerControlLimit, rule);
                                    adjustForStandardDeviation(testGroup, controlLimits, controlLimits.fMean, upperControlLimit, lowerControlLimit);
                                    conditionsMet = checkConditions(testGroup, controlLimits, rule);
                                    attempt++;
                                }
                            }
                            if (attempt >= 5 && !conditionsMet) {
                                System.out.println("  警告: 经过5次尝试仍未满足条件");
                            }
                        } else {
                            System.out.println("  未找到控制限数据");
                        }
                    } else {
                        System.out.println("  缺少必要的映射数据: fTest=" + fTestValue + ", fPrcs=" + fPrcsValue + ", fPart=" + fPartValue);
                    }
                }
                return testGroup;
            }).collect(Collectors.toList());

            long processingEndTime = System.currentTimeMillis();
            System.out.println("数据处理完成，耗时: " + (processingEndTime - processingStartTime) + "ms");

            List<Chemical> modifiedChemicals = processedTestGroups.stream()
                    .flatMap(List::stream)
                    .filter(Chemical::isExamine1Modified)
                    .collect(Collectors.toList());

            System.out.println("开始数据库更新，修改记录数: " + modifiedChemicals.size());
            long updateStartTime = System.currentTimeMillis();

            // 批量更新数据库
            updateExamine1InDatabase(localConnection, modifiedChemicals);

            long updateEndTime = System.currentTimeMillis();
            System.out.println("数据库更新完成，耗时: " + (updateEndTime - updateStartTime) + "ms");

            return processedTestGroups;
        }
    }

    private static Chemical mapRowToChemical(ResultSet resultSet) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        // 创建 Chemical 对象
        Chemical chemical = new Chemical();

        // 遍历每一列并设置对应字段值
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            Object value = resultSet.getObject(i);

            switch (columnName) {
                case "id":
                    chemical.setId((String) value);
                    break;
                case "organization_id":
                    chemical.setOrganizationId(value != null ? (Long) value : null);
                    break;
                case "attribute_id":
                    chemical.setAttributeId(value != null ? (Long) value : null);
                    break;
                case "examine_date":
                    chemical.setExamineDate((Timestamp) value);
                    break;
                case "shift":
                    chemical.setShift((String) value);
                    break;
                case "staff":
                    chemical.setStaff((String) value);
                    break;
                case "process_name":
                    chemical.setProcessName((String) value);
                    break;
                case "product_set_name":
                    chemical.setProductSetName((String) value);
                    break;
                case "product_name":
                    chemical.setProductName((String) value);
                    break;
                case "test_set_name":
                    chemical.setTestSetName((String) value);
                    break;
                case "test_name":
                    chemical.setTestName((String) value);
                    break;
                case "sample_size":
                    chemical.setSampleSize((String) value);
                    break;
                case "layer_number":
                    chemical.setLayerNumber((String) value);
                    break;
                case "upper_limit":
                    chemical.setUpperLimit((String) value);
                    break;
                case "median_specification":
                    chemical.setMedianSpecification((String) value);
                    break;
                case "down_limit":
                    chemical.setDownLimit((String) value);
                    break;
                case "examine2":
                    chemical.setExamine2((String) value);
                    break;
                case "created_by":
                    chemical.setCreatedBy((String) value);
                    break;
                case "last_updated_by":
                    chemical.setLastUpdatedBy((String) value);
                    break;
                case "creation_date":
                    chemical.setCreationDate((Timestamp) value);
                    break;
                case "last_update_date":
                    chemical.setLastUpdateDate((Timestamp) value);
                    break;
                case "status":
                    chemical.setStatus(value != null ? (Long) value : null);
                    break;
                case "frequency":
                    chemical.setFrequency((String) value);
                    break;
                case "frequency_unit":
                    chemical.setFrequencyUnit((String) value);
                    break;
                case "slot_body_name":
                    chemical.setSlotBodyName((String) value);
                    break;
                case "project_team_code":
                    chemical.setProjectTeamCode((String) value);
                    break;
                case "project_team_name":
                    chemical.setProjectTeamName((String) value);
                    break;
                case "test_code":
                    chemical.setTestCode((String) value);
                    break;
                case "adjustment_upper_limit":
                    chemical.setAdjustmentUpperLimit((String) value);
                    break;
                case "adjustment_mid":
                    chemical.setAdjustmentMid((String) value);
                    break;
                case "adjustment_lower_limit":
                    chemical.setAdjustmentLowerLimit((String) value);
                    break;
                case "project_unit":
                    chemical.setProjectUnit((String) value);
                    break;
                case "examine1":
                    chemical.setExamine1((String) value);
                    break;
                case "warning_upper_limit":
                    chemical.setWarningUpperLimit((String) value);
                    break;
                case "warning_mid":
                    chemical.setWarningMid((String) value);
                    break;
                case "warning_lower_limit":
                    chemical.setWarningLowerLimit((String) value);
                    break;
                case "examine1_zs":
                    chemical.setExamine1ZS((String) value);
                    break;
                default:
                    // 如果有未处理的字段，可以在这里添加逻辑或记录日志
                    break;
            }

        }

        return chemical;
    }
    private static void adjustForControlLimits(List<Chemical> chemicals, double mean, double upperLimit, double lowerLimit) {
        double upperAdjustmentRange = (upperLimit - mean) * 0.5;
        double lowerAdjustmentRange = (mean - lowerLimit) * 0.5;

        for (Chemical chemical : chemicals) {
            double examine1 = Double.parseDouble(chemical.getExamine1());
            double examine2 = Double.parseDouble(chemical.getExamine2());

            if (examine1 > upperLimit) {
                // 调整到上限至中值5%之间的任意值
                double adjustedValue = upperLimit - (random.nextDouble() * upperAdjustmentRange);
                double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedValue * examine2 / examine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度

                chemical.setExamine1(String.format("%.3f", adjustedValue));
                chemical.setExamine2(String.format("%.3f", adjustedExamine2));

            } else if (examine1 < lowerLimit) {
                // 调整到下限至中值5%之间的任意值
                double adjustedValue = lowerLimit + (random.nextDouble() * lowerAdjustmentRange);
                double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedValue * examine2 / examine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度

                chemical.setExamine1(String.format("%.3f", adjustedValue));
                chemical.setExamine2(String.format("%.3f", adjustedExamine2));
            }
        }
    }

    private static void adjustDataForCPKWithNewMethod(List<Chemical> chemicals, Rule rule) {
        if (chemicals.isEmpty()) {
            return;
        }

        double average = chemicals.stream()
                .mapToDouble(c -> Double.parseDouble(c.getExamine1()))
                .average()
                .orElse(0.0); // 计算均值

        double cpk = calculateCPK(chemicals, rule);
        int adjustmentCount = 0;
        while (cpk < 1.33 && !chemicals.isEmpty() && adjustmentCount < 15) {
            // 找到与均值差距最大的 Chemical
            Chemical furthest = chemicals.stream()
                    .max(Comparator.comparingDouble(c -> Math.abs(Double.parseDouble(c.getExamine1()) - average)))
                    .orElse(null);
            double originalExamine1 = Double.parseDouble(furthest.getExamine1());
            if (furthest != null) {
                double adjustValue = average + (random.nextDouble() - 0.5) * 0.1 * average; // 调整到均值附近
                furthest.setExamine1(String.format("%.3f", adjustValue));
                double examine2;
                try {
                    examine2 = Double.parseDouble(furthest.getExamine2());
                } catch (NumberFormatException e) {
                    examine2 = 0; // 如果examine2不是有效的数字，则设置为0
                }
                double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度
                furthest.setExamine2(String.format("%.3f", adjustedExamine2));
                // 重新计算 CPK
                cpk = calculateCPK(chemicals, rule);
            }
            adjustmentCount++;
        }
    }

    private static Rule getRuleForTest(Connection localConnection, String productName, String processName, String testName) {
        Rule rule = new Rule(); // 默认所有检查都处理
        try {
            String sql = "SELECT * FROM dbo.[rule] WHERE product_name = ? AND process_name = ? AND test_name = ?";
            try (PreparedStatement stmt = localConnection.prepareStatement(sql)) {
                stmt.setString(1, productName);
                stmt.setString(2, processName);
                stmt.setString(3, testName);
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    rule.productName = rs.getString("product_name");
                    rule.processName = rs.getString("process_name");
                    rule.testName = rs.getString("test_name");
                    rule.isRefresh = rs.getBoolean("is_refresh");
                    return rule;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void adjustForStandardDeviation(List<Chemical> chemicals, ControlLimits controlLimits, double mean, double upperLimit, double lowerLimit) {
        double standardDeviation = controlLimits.fSp;
        double threeSigma = 3 * standardDeviation;
        List<Double> examine1Values = chemicals.stream()
                .map(c -> parseDoubleWithDefault(c.getExamine1(), 0))
                .collect(Collectors.toList());
        List<Double> examine2Values = chemicals.stream()
                .map(c -> parseDoubleWithDefault(c.getExamine2(), 0))
                .collect(Collectors.toList());
        for (int i = 0; i < examine1Values.size() - 1; i++) {
            double currentValue = examine1Values.get(i);
            double nextValue = examine1Values.get(i + 1);
            double examine2 = examine2Values.get(i + 1);
            double difference = Math.abs(nextValue - currentValue);

            if (difference > threeSigma) {
                // 确定哪个值更远离中值
                Chemical toAdjust = Math.abs(currentValue - mean) > Math.abs(nextValue - mean) ? chemicals.get(i) : chemicals.get(i + 1);
                double originalExamine1 = Math.abs(currentValue - mean) > Math.abs(nextValue - mean) ? currentValue : nextValue;
                double adjustValue = calculateAdjustValue(mean, upperLimit, lowerLimit, random);
                double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0;
                // 更新调整后的值
                toAdjust.setExamine1(String.format("%.3f", adjustValue));
                toAdjust.setExamine2(String.format("%.3f", adjustedExamine2));
            }
        }
    }

    private static double calculateAdjustValue(double mean, double upperLimit, double lowerLimit, Random random) {
        double rangeUpper = (upperLimit - mean) * 0.1;
        double rangeLower = (mean - lowerLimit) * 0.1;

        double adjustValue = mean + (random.nextBoolean() ? 1 : -1) * random.nextDouble() * (random.nextBoolean() ? rangeUpper : rangeLower);

        return Math.min(Math.max(adjustValue, lowerLimit), upperLimit);
    }


    private static boolean checkConditions(List<Chemical> chemicals, ControlLimits controlLimits, Rule rule) {
        if (chemicals.isEmpty()) {
            return false;
        }

        double cpk = calculateCPK(chemicals, rule);
        boolean cpkConditionMet = cpk >= 1.33;
        if (!cpkConditionMet) {
            return false;
        }

        boolean noNinePointException = true;
        boolean noSixPointTrend = true;
        boolean noPointExceedsThreeSigma = true;

        double mean = controlLimits.fMean;
        double threeSigma = 3 * controlLimits.fSp;
        int length = chemicals.size();

        double[] values = chemicals.stream().mapToDouble(c -> Double.parseDouble(c.getExamine1())).toArray();

        for (int i = 0; i < length; i++) {
            if (i < length - 1 && Math.abs(values[i + 1] - values[i]) > threeSigma) {
                noPointExceedsThreeSigma = false;
            }

            if (noNinePointException && i <= length - 9) {
                boolean allAbove = true;
                boolean allBelow = true;
                for (int j = i; j < i + 9; j++) {
                    if (values[j] <= mean) {
                        allAbove = false;
                    }
                    if (values[j] >= mean) {
                        allBelow = false;
                    }
                    if (!allAbove && !allBelow) {
                        break;
                    }
                }
                if (allAbove || allBelow) {
                    noNinePointException = false;
                }
            }

            if (noSixPointTrend && i <= length - 6) {
                boolean increasing = true;
                boolean decreasing = true;
                for (int j = i; j < i + 5; j++) {
                    if (values[j] <= values[j + 1]) {
                        decreasing = false;
                    }
                    if (values[j] >= values[j + 1]) {
                        increasing = false;
                    }
                    if (!increasing && !decreasing) {
                        break;
                    }
                }
                if (increasing || decreasing) {
                    noSixPointTrend = false;
                }
            }

            if (!noPointExceedsThreeSigma && !noNinePointException && !noSixPointTrend) {
                return false;
            }
        }

        return noPointExceedsThreeSigma && noNinePointException && noSixPointTrend;
    }

    private static void applySPCRules(List<Chemical> chemicals, ControlLimits controlLimits) {
        double intermediateValue = 6 * controlLimits.fSp;
        double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
        double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
        // 应用9点同侧规则
        checkAndAdjustNinePointsSameSide(chemicals, controlLimits.fMean, upperControlLimit, lowerControlLimit);

        // 应用6点连续上升或下降规则
        checkAndAdjustSixPointsTrend(chemicals);
    }

    private static void checkAndAdjustNinePointsSameSide(List<Chemical> chemicals, double mean, double upperLimit, double lowerLimit) {
        double rangeLower = (mean - lowerLimit) * 0.1;
        double rangeUpper = (upperLimit - mean) * 0.1;

        for (int start = 0; start <= chemicals.size() - 9; start++) {
            boolean allAbove = true;
            boolean allBelow = true;

            for (int i = start; i < start + 9; i++) {
                double examine1 = Double.parseDouble(chemicals.get(i).getExamine1());
                if (examine1 <= mean) {
                    allAbove = false;
                }
                if (examine1 >= mean) {
                    allBelow = false;
                }
                if (!allAbove && !allBelow) {
                    break;  // 优化点2：一旦确定不符合任一条件，立即中断内循环
                }
            }

            if (allAbove || allBelow) {
                int[] adjustIndexes = {start + 1, start + 2, start + 6, start + 7, start + 8}; // 第2、3、7、8、9个点的索引
                for (int index : adjustIndexes) {
                    Chemical chemicalToAdjust = chemicals.get(index);
                    double originalExamine1 = Double.parseDouble(chemicalToAdjust.getExamine1()); // 修改前的examine1值
                    double examine2=0;
                    examine2 = Double.parseDouble(chemicalToAdjust.getExamine2());
                    double adjustValue = mean + (allBelow ? rangeUpper : -rangeLower) + random.nextDouble() * (allBelow ? rangeUpper : rangeLower);
                    adjustValue = Math.min(Math.max(adjustValue, lowerLimit + rangeLower), upperLimit - rangeUpper);  // 确保值在限定范围内
                    double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度
                    // 更新调整后的值
                    chemicalToAdjust.setExamine1(String.format("%.3f", adjustValue)); // 更新examine1，保留三位小数
                    chemicalToAdjust.setExamine2(String.format("%.3f", adjustedExamine2)); // 更新examine2，保留三位小数
                }
            }
        }
    }


    private static void checkAndAdjustSixPointsTrend(List<Chemical> chemicals) {
        for (int start = 0; start <= chemicals.size() - 6; start++) {
            boolean increasing = true;
            boolean decreasing = true;
            double[] examine1Values = new double[6]; // 存储连续6个点的examine1值

            // 预先填充 examine1 数组，避免在比较循环中重复调用 Double.parseDouble
            for (int i = 0; i < 6; i++) {
                try {
                    examine1Values[i] = Double.parseDouble(chemicals.get(start + i).getExamine1());
                } catch (NumberFormatException e) {
                    examine1Values[i] = 0; // 处理非数值情况
                }
            }

            for (int i = 0; i < 5; i++) { // 只需检查前5个，因为要比较的是当前点和下一个点
                if (examine1Values[i] >= examine1Values[i + 1]) {
                    increasing = false;
                }
                if (examine1Values[i] <= examine1Values[i + 1]) {
                    decreasing = false;
                }
                if (!increasing && !decreasing) { // 一旦确定趋势被打破，立即退出循环
                    break;
                }
            }

            if (increasing || decreasing) {
                int adjustIndex = start + 2; // 第3个点的索引
                Chemical chemicalToAdjust = chemicals.get(adjustIndex);
                double previousExamine1 = examine1Values[1]; // 使用预加载的数据
                double currentExamine1 = examine1Values[2];
                double adjustValue;

                if (increasing) {
                    adjustValue = currentExamine1 - (currentExamine1 - previousExamine1) * 1.2;
                } else {
                    adjustValue = currentExamine1 + (previousExamine1 - currentExamine1) * 1.2;
                }

                double examine2 = 0;
                try {
                    examine2 = Double.parseDouble(chemicalToAdjust.getExamine2());
                } catch (NumberFormatException e) {
                    examine2 = 0; // 如果examine2不是有效的数字，则设置为0
                }
                double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / currentExamine1 * 1000.0) / 1000.0 : 0;
                chemicalToAdjust.setExamine1(String.format("%.3f", adjustValue));
                chemicalToAdjust.setExamine2(String.format("%.3f", adjustedExamine2));
            }
        }
    }

    private static double calculateCPK(List<Chemical> chemicals, Rule rule) {
        if (chemicals == null || chemicals.isEmpty()) {
            return Double.NaN;
        }
        // 假设Chemical类中有getExamine1方法返回检查值的double类型
        double[] values = chemicals.stream().mapToDouble(c -> Double.parseDouble(c.getExamine1())).toArray();

        // 计算均值和标准差
        double mean = Arrays.stream(values).average().orElse(Double.NaN);
        double stdDev = Math.sqrt(Arrays.stream(values).map(v -> Math.pow(v - mean, 2)).sum() / (values.length - 1));

        // 获取规格上限
        double usl = Double.parseDouble(chemicals.get(0).getUpperLimit());

        double cpk;
        if (rule != null) {
            // 如果存在规则，仅考虑规格上限
            cpk = (usl - mean) / (3 * stdDev);
        } else {
            // 如果不存在规则，同时考虑规格上下限
            double lsl = Double.parseDouble(chemicals.get(0).getDownLimit());
            cpk = Math.min((usl - mean) / (3 * stdDev), (mean - lsl) / (3 * stdDev));
        }
        return cpk;
    }

    private static void adjustDataForCPK(List<Chemical> chemicals, double mean, double upperLimit, double lowerLimit, Rule rule) {
        if (chemicals.isEmpty()) {
            return; // 如果列表为空，则不执行任何调整
        }

        // 预先计算并存储 examine1 值和转换
        List<Double> examine1Values = chemicals.stream()
                .map(chemical -> parseDoubleWithDefault(chemical.getExamine1(), 0.0))
                .collect(Collectors.toList());

        double cpk = calculateCPK(chemicals, rule);

        int adjustmentCount = 0;
        int furthestIndex;
        double maxDeviation, deviation;
        Chemical furthest;
        double originalExamine1, adjustValue, examine2, adjustedExamine2;

        while (cpk < 1.33 && adjustmentCount < 15) {
            furthestIndex = 0;
            maxDeviation = 0;

            // 寻找最远离中值的 Chemical
            for (int i = 0; i < examine1Values.size(); i++) {
                deviation = Math.abs(examine1Values.get(i) - mean);
                if (deviation > maxDeviation) {
                    maxDeviation = deviation;
                    furthestIndex = i;
                }
            }

            furthest = chemicals.get(furthestIndex);
            originalExamine1 = examine1Values.get(furthestIndex);

            // 调整值的计算
            adjustValue = mean + (random.nextDouble() - 0.5) * (mean - lowerLimit) * 0.2;
            adjustValue = Math.max(Math.min(adjustValue, upperLimit), lowerLimit);

            // 计算调整后的examine2值
            examine2 = parseDoubleWithDefault(furthest.getExamine2(), 0);
            adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0;

            // 更新调整后的值
            furthest.setExamine1(String.format("%.3f", adjustValue));
            furthest.setExamine2(String.format("%.3f", adjustedExamine2)); // 更新examine2，保留三位小数

            // 更新 examine1Values 列表
            examine1Values.set(furthestIndex, adjustValue);

            // 重新计算CPK
            cpk = calculateCPK(chemicals, rule);
            System.out.println("CPK2: " + cpk);
            adjustmentCount++;

            System.out.printf("Adjusted data for CPK: %.3f, getExamine1: %.3f for chemical %s %s %s ID:%s. Adjusted examine2: %.3f%n",
                    cpk, adjustValue, furthest.getProcessName(), furthest.getProductName(), furthest.getTestName(), furthest.getId(), adjustedExamine2);
        }
    }

    private static double parseDoubleWithDefault(String s, double defaultValue) {
        try {
            return Double.parseDouble(s);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }


    private static void updateExamine1InDatabase(Connection connection, List<Chemical> modifiedChemicals) {
        if (modifiedChemicals.isEmpty()) {
            System.out.println("没有需要更新的记录");
            return;
        }

        String sql = "UPDATE chemical_ys SET examine1 = ?, examine2 = ? WHERE id = ?";
        int batchSize = 100; // 批量大小
        int totalUpdated = 0;

        try {
            connection.setAutoCommit(false); // 开启事务

            try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                for (int i = 0; i < modifiedChemicals.size(); i++) {
                    Chemical chemical = modifiedChemicals.get(i);

                    preparedStatement.setString(1, chemical.getExamine1());
                    preparedStatement.setString(2, chemical.getExamine2());
                    preparedStatement.setString(3, chemical.getId());
                    preparedStatement.addBatch();

                    // 每100条记录执行一次批量更新
                    if ((i + 1) % batchSize == 0 || i == modifiedChemicals.size() - 1) {
                        int[] results = preparedStatement.executeBatch();
                        int batchUpdated = Arrays.stream(results).sum();
                        totalUpdated += batchUpdated;

                        System.out.println("批量更新完成: " + (i + 1) + "/" + modifiedChemicals.size() +
                                         " (本批次更新: " + batchUpdated + " 条)");
                        preparedStatement.clearBatch();
                    }
                }

                connection.commit(); // 提交事务
                System.out.println("数据库更新成功，总计更新: " + totalUpdated + " 条记录");

            } catch (SQLException e) {
                connection.rollback(); // 回滚事务
                System.err.println("批量更新失败，已回滚: " + e.getMessage());
                throw e;
            }

        } catch (SQLException e) {
            System.err.println("数据库更新异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                connection.setAutoCommit(true); // 恢复自动提交
            } catch (SQLException e) {
                System.err.println("恢复自动提交失败: " + e.getMessage());
            }
        }
    }

    private static class ControlLimits {
        double fMean;
        double fSp;

        public ControlLimits(double fMean, double fSp) {
            this.fMean = fMean;
            this.fSp = fSp;
        }
    }

    static class SpecLimits {
        double fUsl;
        double fLsl;

        public SpecLimits(double fUsl, double fLsl) {
            this.fUsl = fUsl;
            this.fLsl = fLsl;
        }
    }
    // 新增获取规格值的类
    static class SpecLimitsCache {
        private static final Map<String, SpecLimits> cache = new ConcurrentHashMap<>();

        public static SpecLimits getSpecLimits(Connection remoteConnection, String fPrcs, String fTest, String fPart) {
            // 构建一个唯一的键来标识每个不同的参数组合
            String key = fPrcs + "_" + fTest + "_" + fPart;

            // 首先尝试从缓存中获取
            if (cache.containsKey(key)) {
                return cache.get(key);
            }

            // 如果缓存中没有，则从数据库查询并更新缓存
            if (fPrcs != null && fTest != null && fPart != null) {
                String sql = "SELECT F_USL, F_LSL FROM dbo.SPEC_LIM WHERE F_PRCS = ? AND F_TEST = ? AND F_PART = ? " +
                        "ORDER BY F_EDTM DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
                try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
                    preparedStatement.setString(1, fPrcs);
                    preparedStatement.setString(2, fTest);
                    preparedStatement.setString(3, fPart);
                    try (ResultSet resultSet = preparedStatement.executeQuery()) {
                        if (resultSet.next()) {
                            double fUsl = resultSet.getDouble("F_USL");
                            double fLsl = resultSet.getDouble("F_LSL");
                            SpecLimits limits = new SpecLimits(fUsl, fLsl);
                            cache.put(key, limits); // 更新缓存
                            return limits;
                        }
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
            return null;
        }
    }


}
