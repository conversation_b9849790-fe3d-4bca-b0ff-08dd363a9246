package org.example;


import lombok.Data;


import java.io.Serializable;
import java.sql.Timestamp;

/**
 * (Chemical)实体类
 *
 * <AUTHOR>
 * @since 2023-12-22 16:00:58
 */

@Data
public class Chemical implements Serializable {
    private static final long serialVersionUID = -99950014482622591L;
    
    private String id;
    
    private Long organizationId;
    
    private Long attributeId;
    
    private Timestamp examineDate;
    
    private String shift;
    
    private String staff;
    
    private String departmentCode;
    
    private String processSetName;
    
    private String processName;
    
    private String productSetName;
    
    private String productName;
    
    private String testSetName;
    
    private String testName;
    
    private String sampleSize;
    
    private String layerNumber;
    
    private String upperLimit;
    
    private String medianSpecification;
    
    private String downLimit;
    
    private String examine1;

    private String examine1ZS;

    private String examine2;
    
    private String createdBy;
    
    private String lastUpdatedBy;
    
    private Timestamp creationDate;
    
    private Timestamp lastUpdateDate;
    
    private Long status;
    
    private String frequency;
    
    private String frequencyUnit;
    
    private String slotBodyName;
    
    private String projectTeamCode;
    
    private String projectTeamName;
    
    private String testCode;
    
    private String adjustmentUpperLimit;
    
    private String adjustmentMid;
    
    private String adjustmentLowerLimit;
    
    private String projectUnit;

    private Boolean isExported;

    private String warningUpperLimit;

    private String warningMid;

    private String warningLowerLimit;

    private boolean examine1Modified; // 添加这个字段来跟踪examine1是否被修改

    // 其他属性和方法

    // 设置 examine1 值时，检查是否真的有变化
    public void setExamine1(String newExamine1) {
        // 只有当新值与旧值不相等时，才标记为已修改
        if (this.examine1 != null && !this.examine1.equals(newExamine1)) {
            this.examine1 = newExamine1;
            this.examine1Modified = true; // 标记为已修改
        } else {
            this.examine1 = newExamine1;
        }
    }

    public boolean isExamine1Modified() {
        return examine1Modified;
    }

    // Getter and Setter methods
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public Long getOrganizationId() { return organizationId; }
    public void setOrganizationId(Long organizationId) { this.organizationId = organizationId; }

    public Long getAttributeId() { return attributeId; }
    public void setAttributeId(Long attributeId) { this.attributeId = attributeId; }

    public Timestamp getExamineDate() { return examineDate; }
    public void setExamineDate(Timestamp examineDate) { this.examineDate = examineDate; }

    public String getShift() { return shift; }
    public void setShift(String shift) { this.shift = shift; }

    public String getStaff() { return staff; }
    public void setStaff(String staff) { this.staff = staff; }

    public String getDepartmentCode() { return departmentCode; }
    public void setDepartmentCode(String departmentCode) { this.departmentCode = departmentCode; }

    public String getProcessSetName() { return processSetName; }
    public void setProcessSetName(String processSetName) { this.processSetName = processSetName; }

    public String getProcessName() { return processName; }
    public void setProcessName(String processName) { this.processName = processName; }

    public String getProductSetName() { return productSetName; }
    public void setProductSetName(String productSetName) { this.productSetName = productSetName; }

    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }

    public String getTestSetName() { return testSetName; }
    public void setTestSetName(String testSetName) { this.testSetName = testSetName; }

    public String getTestName() { return testName; }
    public void setTestName(String testName) { this.testName = testName; }

    public String getSampleSize() { return sampleSize; }
    public void setSampleSize(String sampleSize) { this.sampleSize = sampleSize; }

    public String getLayerNumber() { return layerNumber; }
    public void setLayerNumber(String layerNumber) { this.layerNumber = layerNumber; }

    public String getUpperLimit() { return upperLimit; }
    public void setUpperLimit(String upperLimit) { this.upperLimit = upperLimit; }

    public String getMedianSpecification() { return medianSpecification; }
    public void setMedianSpecification(String medianSpecification) { this.medianSpecification = medianSpecification; }

    public String getDownLimit() { return downLimit; }
    public void setDownLimit(String downLimit) { this.downLimit = downLimit; }

    public String getExamine1() { return examine1; }
    // setExamine1 is already defined above with modification tracking

    public String getExamine1ZS() { return examine1ZS; }
    public void setExamine1ZS(String examine1ZS) { this.examine1ZS = examine1ZS; }

    public String getExamine2() { return examine2; }
    public void setExamine2(String examine2) { this.examine2 = examine2; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public String getLastUpdatedBy() { return lastUpdatedBy; }
    public void setLastUpdatedBy(String lastUpdatedBy) { this.lastUpdatedBy = lastUpdatedBy; }

    public Timestamp getCreationDate() { return creationDate; }
    public void setCreationDate(Timestamp creationDate) { this.creationDate = creationDate; }

    public Timestamp getLastUpdateDate() { return lastUpdateDate; }
    public void setLastUpdateDate(Timestamp lastUpdateDate) { this.lastUpdateDate = lastUpdateDate; }

    public Long getStatus() { return status; }
    public void setStatus(Long status) { this.status = status; }

    public String getFrequency() { return frequency; }
    public void setFrequency(String frequency) { this.frequency = frequency; }

    public String getFrequencyUnit() { return frequencyUnit; }
    public void setFrequencyUnit(String frequencyUnit) { this.frequencyUnit = frequencyUnit; }

    public String getSlotBodyName() { return slotBodyName; }
    public void setSlotBodyName(String slotBodyName) { this.slotBodyName = slotBodyName; }

    public String getProjectTeamCode() { return projectTeamCode; }
    public void setProjectTeamCode(String projectTeamCode) { this.projectTeamCode = projectTeamCode; }

    public String getProjectTeamName() { return projectTeamName; }
    public void setProjectTeamName(String projectTeamName) { this.projectTeamName = projectTeamName; }

    public String getTestCode() { return testCode; }
    public void setTestCode(String testCode) { this.testCode = testCode; }

    public String getAdjustmentUpperLimit() { return adjustmentUpperLimit; }
    public void setAdjustmentUpperLimit(String adjustmentUpperLimit) { this.adjustmentUpperLimit = adjustmentUpperLimit; }

    public String getAdjustmentMid() { return adjustmentMid; }
    public void setAdjustmentMid(String adjustmentMid) { this.adjustmentMid = adjustmentMid; }

    public String getAdjustmentLowerLimit() { return adjustmentLowerLimit; }
    public void setAdjustmentLowerLimit(String adjustmentLowerLimit) { this.adjustmentLowerLimit = adjustmentLowerLimit; }

    public String getProjectUnit() { return projectUnit; }
    public void setProjectUnit(String projectUnit) { this.projectUnit = projectUnit; }

    public Boolean getIsExported() { return isExported; }
    public void setIsExported(Boolean isExported) { this.isExported = isExported; }

    public String getWarningUpperLimit() { return warningUpperLimit; }
    public void setWarningUpperLimit(String warningUpperLimit) { this.warningUpperLimit = warningUpperLimit; }

    public String getWarningMid() { return warningMid; }
    public void setWarningMid(String warningMid) { this.warningMid = warningMid; }

    public String getWarningLowerLimit() { return warningLowerLimit; }
    public void setWarningLowerLimit(String warningLowerLimit) { this.warningLowerLimit = warningLowerLimit; }
}

