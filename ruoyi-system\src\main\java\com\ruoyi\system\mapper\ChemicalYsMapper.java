package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.ChemicalYs;

/**
 * 化学应审数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ChemicalYsMapper 
{
    /**
     * 查询化学应审数据
     * 
     * @param id 化学应审数据主键
     * @return 化学应审数据
     */
    public ChemicalYs selectChemicalYsById(String id);

    /**
     * 查询化学应审数据列表
     * 
     * @param chemicalYs 化学应审数据
     * @return 化学应审数据集合
     */
    public List<ChemicalYs> selectChemicalYsList(ChemicalYs chemicalYs);

    /**
     * 新增化学应审数据
     * 
     * @param chemicalYs 化学应审数据
     * @return 结果
     */
    public int insertChemicalYs(ChemicalYs chemicalYs);

    /**
     * 修改化学应审数据
     * 
     * @param chemicalYs 化学应审数据
     * @return 结果
     */
    public int updateChemicalYs(ChemicalYs chemicalYs);

    /**
     * 删除化学应审数据
     * 
     * @param id 化学应审数据主键
     * @return 结果
     */
    public int deleteChemicalYsById(String id);

    /**
     * 批量删除化学应审数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChemicalYsByIds(String[] ids);

    /**
     * 查询导出数据列表
     * 
     * @param chemicalYs 查询条件
     * @return 化学应审数据集合
     */
    public List<ChemicalYs> selectExportChemicalYsList(ChemicalYs chemicalYs);

    /**
     * 按时间范围查询数据
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @return 化学应审数据集合
     */
    public List<ChemicalYs> selectChemicalYsByDateRange(Date startDate, Date endDate, String[] layerNumbers);

    /**
     * 按工艺分组查询数据
     * 
     * @param chemicalYs 查询条件
     * @return 化学应审数据集合
     */
    public List<ChemicalYs> selectChemicalYsGroupByProcess(ChemicalYs chemicalYs);

    /**
     * 刷新数据
     * 
     * @param chemicalYs 化学应审数据
     * @return 结果
     */
    public int refreshChemicalYsData(ChemicalYs chemicalYs);

    /**
     * 批量刷新数据
     * 
     * @param chemicalYsList 化学应审数据列表
     * @return 结果
     */
    public int batchRefreshChemicalYsData(List<ChemicalYs> chemicalYsList);

    /**
     * 统计应审数据
     * 
     * @param chemicalYs 查询条件
     * @return 统计结果
     */
    public int countChemicalYsData(ChemicalYs chemicalYs);

    /**
     * 统计今日应审数据
     * 
     * @return 今日应审数据数量
     */
    public int countTodayYsData();

    /**
     * 统计修改过的数据
     * 
     * @return 修改过的数据数量
     */
    public int countModifiedData();

    /**
     * 查询特定层号的数据
     * 
     * @param layerNumbers 层号数组
     * @return 化学应审数据集合
     */
    public List<ChemicalYs> selectChemicalYsByLayerNumbers(String[] layerNumbers);
}
