-- =============================================
-- 化学审计系统菜单权限配置
-- 基于若依框架菜单权限体系
-- =============================================

-- 获取当前最大菜单ID，避免冲突
DECLARE @max_menu_id BIGINT;
SELECT @max_menu_id = ISNULL(MAX(menu_id), 2000) FROM sys_menu;

-- 1. 一级菜单：化学审计系统
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 1, '化学审计', 0, 6, 'chemical', NULL, '', 1, 0, 'M', '0', '0', '', 'chemistry', 'admin', GETDATE(), '', NULL, '化学审计系统菜单');

-- 2. 二级菜单：数据监控
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 2, '数据监控', @max_menu_id + 1, 1, 'monitor', 'chemical/monitor/index', '', 1, 0, 'C', '0', '0', 'chemical:monitor:view', 'monitor', 'admin', GETDATE(), '', NULL, '化学数据监控页面');

-- 3. 二级菜单：任务控制
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 3, '任务控制', @max_menu_id + 1, 2, 'task', 'chemical/task/index', '', 1, 0, 'C', '0', '0', 'chemical:task:view', 'job', 'admin', GETDATE(), '', NULL, '任务控制管理页面');

-- 4. 二级菜单：数据管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 4, '数据管理', @max_menu_id + 1, 3, 'data', NULL, '', 1, 0, 'M', '0', '0', '', 'table', 'admin', GETDATE(), '', NULL, '数据管理菜单');

-- 5. 三级菜单：原始数据
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 5, '原始数据', @max_menu_id + 4, 1, 'chemical', 'chemical/data/chemical/index', '', 1, 0, 'C', '0', '0', 'chemical:data:view', 'documentation', 'admin', GETDATE(), '', NULL, '化学原始数据管理');

-- 6. 三级菜单：应审数据
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 6, '应审数据', @max_menu_id + 4, 2, 'chemical-ys', 'chemical/data/chemical-ys/index', '', 1, 0, 'C', '0', '0', 'chemical:ys:view', 'edit', 'admin', GETDATE(), '', NULL, '化学应审数据管理');

-- 7. 三级菜单：数据日志
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 7, '数据日志', @max_menu_id + 4, 3, 'log', 'chemical/data/log/index', '', 1, 0, 'C', '0', '0', 'chemical:log:view', 'log', 'admin', GETDATE(), '', NULL, '数据变更日志');

-- 8. 二级菜单：规则管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 8, '规则管理', @max_menu_id + 1, 4, 'rule', 'chemical/rule/index', '', 1, 0, 'C', '0', '0', 'chemical:rule:view', 'guide', 'admin', GETDATE(), '', NULL, '处理规则管理');

-- 9. 二级菜单：数据导出
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 9, '数据导出', @max_menu_id + 1, 5, 'export', 'chemical/export/index', '', 1, 0, 'C', '0', '0', 'chemical:export:view', 'download', 'admin', GETDATE(), '', NULL, '数据导出管理');

-- 10. 二级菜单：系统配置
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 10, '系统配置', @max_menu_id + 1, 6, 'config', 'chemical/config/index', '', 1, 0, 'C', '0', '0', 'chemical:config:view', 'system', 'admin', GETDATE(), '', NULL, '系统参数配置');

-- =============================================
-- 按钮权限配置
-- =============================================

-- 任务控制按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 11, '启动任务', @max_menu_id + 3, 1, '', '', '', 1, 0, 'F', '0', '0', 'chemical:task:start', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 12, '暂停任务', @max_menu_id + 3, 2, '', '', '', 1, 0, 'F', '0', '0', 'chemical:task:pause', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 13, '停止任务', @max_menu_id + 3, 3, '', '', '', 1, 0, 'F', '0', '0', 'chemical:task:stop', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 14, '重启任务', @max_menu_id + 3, 4, '', '', '', 1, 0, 'F', '0', '0', 'chemical:task:restart', '#', 'admin', GETDATE(), '', NULL, '');

-- 原始数据按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 15, '查询', @max_menu_id + 5, 1, '', '', '', 1, 0, 'F', '0', '0', 'chemical:data:query', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 16, '新增', @max_menu_id + 5, 2, '', '', '', 1, 0, 'F', '0', '0', 'chemical:data:add', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 17, '修改', @max_menu_id + 5, 3, '', '', '', 1, 0, 'F', '0', '0', 'chemical:data:edit', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 18, '删除', @max_menu_id + 5, 4, '', '', '', 1, 0, 'F', '0', '0', 'chemical:data:remove', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 19, '导出', @max_menu_id + 5, 5, '', '', '', 1, 0, 'F', '0', '0', 'chemical:data:export', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 20, '重新处理', @max_menu_id + 5, 6, '', '', '', 1, 0, 'F', '0', '0', 'chemical:data:reprocess', '#', 'admin', GETDATE(), '', NULL, '');

-- 应审数据按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 21, '查询', @max_menu_id + 6, 1, '', '', '', 1, 0, 'F', '0', '0', 'chemical:ys:query', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 22, '修改', @max_menu_id + 6, 2, '', '', '', 1, 0, 'F', '0', '0', 'chemical:ys:edit', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 23, '删除', @max_menu_id + 6, 3, '', '', '', 1, 0, 'F', '0', '0', 'chemical:ys:remove', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 24, '导出', @max_menu_id + 6, 4, '', '', '', 1, 0, 'F', '0', '0', 'chemical:ys:export', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 25, '刷新数据', @max_menu_id + 6, 5, '', '', '', 1, 0, 'F', '0', '0', 'chemical:ys:refresh', '#', 'admin', GETDATE(), '', NULL, '');

-- 规则管理按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 26, '查询', @max_menu_id + 8, 1, '', '', '', 1, 0, 'F', '0', '0', 'chemical:rule:query', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 27, '新增', @max_menu_id + 8, 2, '', '', '', 1, 0, 'F', '0', '0', 'chemical:rule:add', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 28, '修改', @max_menu_id + 8, 3, '', '', '', 1, 0, 'F', '0', '0', 'chemical:rule:edit', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 29, '删除', @max_menu_id + 8, 4, '', '', '', 1, 0, 'F', '0', '0', 'chemical:rule:remove', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 30, '导出', @max_menu_id + 8, 5, '', '', '', 1, 0, 'F', '0', '0', 'chemical:rule:export', '#', 'admin', GETDATE(), '', NULL, '');

-- 数据导出按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 31, '导出CSV', @max_menu_id + 9, 1, '', '', '', 1, 0, 'F', '0', '0', 'chemical:export:csv', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 32, '导出Excel', @max_menu_id + 9, 2, '', '', '', 1, 0, 'F', '0', '0', 'chemical:export:excel', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 33, '下载文件', @max_menu_id + 9, 3, '', '', '', 1, 0, 'F', '0', '0', 'chemical:export:download', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 34, '删除记录', @max_menu_id + 9, 4, '', '', '', 1, 0, 'F', '0', '0', 'chemical:export:remove', '#', 'admin', GETDATE(), '', NULL, '');

-- 系统配置按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(@max_menu_id + 35, '查询', @max_menu_id + 10, 1, '', '', '', 1, 0, 'F', '0', '0', 'chemical:config:query', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 36, '新增', @max_menu_id + 10, 2, '', '', '', 1, 0, 'F', '0', '0', 'chemical:config:add', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 37, '修改', @max_menu_id + 10, 3, '', '', '', 1, 0, 'F', '0', '0', 'chemical:config:edit', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 38, '删除', @max_menu_id + 10, 4, '', '', '', 1, 0, 'F', '0', '0', 'chemical:config:remove', '#', 'admin', GETDATE(), '', NULL, ''),
(@max_menu_id + 39, '刷新缓存', @max_menu_id + 10, 5, '', '', '', 1, 0, 'F', '0', '0', 'chemical:config:refresh', '#', 'admin', GETDATE(), '', NULL, '');

-- =============================================
-- 角色权限分配 (为管理员角色分配所有权限)
-- =============================================

-- 为管理员角色(role_id=1)分配化学审计系统的所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_id BETWEEN @max_menu_id + 1 AND @max_menu_id + 39;
